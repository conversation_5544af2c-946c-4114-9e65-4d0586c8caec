# 🚀 语雀下载工具 v2.1.0 发布说明

## 📅 发布信息
- **版本号**: v2.1.0
- **发布日期**: 2025年7月4日
- **分发包**: `yuque-downloader-v2.1.0-20250704_173606.zip`

## ✨ 新功能亮点

### 🎉 Excel文档自动下载支持
- **功能**: 现在支持自动下载Excel表格文档
- **格式**: 正确生成 `.xlsx` 文件扩展名
- **流程**: 与Word文档使用相同的下载流程，无需特殊操作

### 🔧 文件名扩展名修复
- **问题**: 之前Excel文档错误显示为 `.docx` 扩展名
- **修复**: 现在Excel文档正确显示为 `.xlsx` 扩展名
- **影响**: 用户可以直接通过文件扩展名识别文件类型

### 📊 统一文档处理
- **改进**: Excel文档与其他文档类型使用统一的处理流程
- **优化**: 移除了Excel文档的特殊跳过逻辑
- **体验**: 用户界面更加一致，无需额外操作

## 🔧 技术改进

### API客户端优化
- **新增**: Excel文档专用导出配置 (`type: "excel"`)
- **新增**: Excel URL构建逻辑，支持相对URL到完整URL的转换
- **优化**: 环境自适应，支持企业版和公版语雀

### 文件管理增强
- **新增**: Excel文件扩展名支持 (`.xlsx`)
- **移除**: Excel跳过状态的统计逻辑
- **优化**: 下载摘要统计更加准确

### 用户界面改进
- **移除**: Sheet文档手动导出提示
- **优化**: 统一的文档类型处理提示
- **改进**: 用户体验更加流畅

## 📋 支持的文档类型

| 文档类型 | 扩展名 | 状态 | 说明 |
|---------|--------|------|------|
| Word文档 | `.docx` | ✅ 支持 | 普通文档和富文本内容 |
| Excel表格 | `.xlsx` | ✅ 新增 | 数据表格和图表 |
| PDF文档 | `.pdf` | ✅ 支持 | 根据导出设置 |
| Markdown | `.md` | ✅ 支持 | 根据导出设置 |
| Board文档 | - | ⏭️ 跳过 | 需要手动处理 |

## 🛠️ 修复的问题

### 文件名扩展名错误
- **问题**: Excel文档显示为 `文档名.docx` 而不是 `文档名.xlsx`
- **原因**: 文件名生成时没有传递正确的文档类型参数
- **修复**: 根据文档类型自动选择正确的导出类型和扩展名

### Excel文档跳过逻辑
- **问题**: Excel文档被自动跳过，需要手动下载
- **原因**: 之前的实现中Excel文档被特殊处理
- **修复**: 移除跳过逻辑，实现自动下载

## 📦 分发包内容

### 核心文件
- 所有Python源代码文件（包含最新修改）
- 配置文件和依赖列表
- 启动脚本（Windows/macOS/Linux）

### 文档文件
- `README.md` - 详细使用说明
- `INSTALL.md` - 安装指南
- `QUICKSTART.md` - 快速开始指南（更新）
- `EXCEL_DOWNLOAD_SUMMARY.md` - Excel功能详细说明 ✨新增
- `FILENAME_EXTENSION_FIX.md` - 文件名修复说明 ✨新增

### 目录结构
- `downloads/` - 下载文件存储目录
- `drivers/` - ChromeDriver存储目录
- `logs/` - 程序日志目录

## 🚀 使用方法

### 快速启动
1. **解压分发包**到任意目录
2. **Windows用户**: 双击 `start.bat`
3. **macOS/Linux用户**: 运行 `./start.sh`

### 首次使用
1. 程序会自动检查Python环境和依赖
2. 在打开的浏览器中登录语雀账号
3. 选择要下载的知识库
4. 选择下载模式（全部/精选）
5. 开始自动下载

## 📊 版本对比

| 功能 | v2.0.0 | v2.1.0 |
|------|--------|--------|
| Word文档下载 | ✅ | ✅ |
| Excel文档下载 | ❌ 跳过 | ✅ 自动下载 |
| 文件扩展名 | 部分错误 | ✅ 完全正确 |
| 用户界面 | 有特殊提示 | ✅ 统一体验 |
| 文档完整性 | 基础文档 | ✅ 详细说明 |

## 🔄 升级说明

### 从v2.0.0升级
1. 下载新的v2.1.0分发包
2. 解压到新目录（建议不要覆盖旧版本）
3. 直接使用，无需额外配置
4. 旧版本的下载文件不受影响

### 兼容性
- ✅ 完全向后兼容
- ✅ 不影响已下载的文件
- ✅ 配置文件格式不变

## 📞 技术支持

- **详细文档**: 查看分发包中的 `README.md`
- **安装问题**: 参考 `INSTALL.md`
- **Excel功能**: 查看 `EXCEL_DOWNLOAD_SUMMARY.md`
- **问题反馈**: GitHub Issues

## 🎯 下一步计划

- 支持更多文档格式（如PPT）
- 优化下载速度和稳定性
- 增加批量操作功能
- 改进用户界面体验

---

**💡 提示**: 建议用户阅读 `QUICKSTART.md` 快速上手新功能！
