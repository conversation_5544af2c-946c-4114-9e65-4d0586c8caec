#!/usr/bin/env python3
"""
调试URL类型检测的脚本
"""

import sys
import os
import urllib.parse

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_url_analysis():
    """测试URL分析逻辑"""
    test_url = "https://lark-temp.oss-cn-hangzhou.aliyuncs.com/__temp/0/docx/2a896a05-7f0a-43cc-8ec3-abaa9df5c13a.docx?OSSAccessKeyId=LTAI4GKnqTWmz2X8mzA1Sjbv&Expires=1751626205&Signature=XDC%2FHl8g1JR0nga05%2F%2Bgi307gbU%3D"
    
    print(f"测试URL: {test_url}")
    
    # 手动实现URL类型分析逻辑
    try:
        parsed = urllib.parse.urlparse(test_url)
        params = urllib.parse.parse_qs(parsed.query)
        
        print(f"域名: {parsed.netloc}")
        print(f"路径: {parsed.path}")
        print(f"查询参数: {list(params.keys())}")
        
        if 'filename' in params and 'attachable_type' in params:
            url_type = 'yuque_internal'
        elif 'OSSAccessKeyId' in params and 'Signature' in params:
            url_type = 'oss_standard'
        else:
            url_type = 'unknown'
            
        print(f"URL类型: {url_type}")
        
        # 检查关键参数
        for key in ['OSSAccessKeyId', 'Expires', 'Signature']:
            if key in params:
                print(f"{key}: {params[key][0]}")
        
        return url_type
        
    except Exception as e:
        print(f"分析失败: {e}")
        return 'unknown'

def test_api_client_analysis():
    """测试API客户端的URL分析"""
    try:
        # 创建一个最小的测试环境
        class MockWebDriverManager:
            def __init__(self):
                self.driver = None
            def get_headers(self):
                return {}
            def close(self):
                pass
        
        from api_client import YuqueAPIClient
        
        mock_wm = MockWebDriverManager()
        client = YuqueAPIClient(mock_wm)
        
        test_url = "https://lark-temp.oss-cn-hangzhou.aliyuncs.com/__temp/0/docx/2a896a05-7f0a-43cc-8ec3-abaa9df5c13a.docx?OSSAccessKeyId=LTAI4GKnqTWmz2X8mzA1Sjbv&Expires=1751626205&Signature=XDC%2FHl8g1JR0nga05%2F%2Bgi307gbU%3D"
        
        url_type = client._analyze_url_type(test_url)
        print(f"API客户端检测的URL类型: {url_type}")
        
        # 测试URL有效性检查
        is_valid = client._check_oss_url_validity(test_url)
        print(f"URL有效性: {is_valid}")
        
        client.close()
        
        return url_type
        
    except Exception as e:
        print(f"API客户端测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    print("🔧 URL类型检测调试工具")
    print("=" * 50)
    
    # 测试1: 手动分析
    print("\n1. 手动URL分析:")
    manual_type = test_url_analysis()
    
    # 测试2: API客户端分析
    print("\n2. API客户端分析:")
    api_type = test_api_client_analysis()
    
    # 比较结果
    print(f"\n3. 结果比较:")
    print(f"手动分析结果: {manual_type}")
    print(f"API客户端结果: {api_type}")
    
    if manual_type == api_type:
        print("✅ 结果一致")
    else:
        print("❌ 结果不一致，需要检查API客户端的实现")

if __name__ == "__main__":
    main()
