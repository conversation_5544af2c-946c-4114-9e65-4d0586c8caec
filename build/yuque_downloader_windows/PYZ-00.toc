('/Users/<USER>/PycharmProjects/PythonProject2/build/yuque_downloader_windows/PYZ-00.pyz',
 [('OpenSSL',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/OpenSSL/__init__.py',
   'PYMODULE'),
  ('OpenSSL.SSL',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/OpenSSL/SSL.py',
   'PYMODULE'),
  ('OpenSSL._util',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/OpenSSL/_util.py',
   'PYMODULE'),
  ('OpenSSL.crypto',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/OpenSSL/crypto.py',
   'PYMODULE'),
  ('OpenSSL.version',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/OpenSSL/version.py',
   'PYMODULE'),
  ('__future__',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/__future__.py',
   'PYMODULE'),
  ('_aix_support',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_aix_support.py',
   'PYMODULE'),
  ('_colorize',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_colorize.py',
   'PYMODULE'),
  ('_compat_pickle',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/_distutils_hack/__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/_distutils_hack/override.py',
   'PYMODULE'),
  ('_ios_support',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_ios_support.py',
   'PYMODULE'),
  ('_opcode_metadata',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_opcode_metadata.py',
   'PYMODULE'),
  ('_osx_support',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_osx_support.py',
   'PYMODULE'),
  ('_py_abc',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_py_abc.py',
   'PYMODULE'),
  ('_pydatetime',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pydecimal.py',
   'PYMODULE'),
  ('_pyrepl',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/__init__.py',
   'PYMODULE'),
  ('_pyrepl._minimal_curses',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl._threading_handler',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.commands',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/commands.py',
   'PYMODULE'),
  ('_pyrepl.completing_reader',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/completing_reader.py',
   'PYMODULE'),
  ('_pyrepl.console',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/console.py',
   'PYMODULE'),
  ('_pyrepl.curses',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/curses.py',
   'PYMODULE'),
  ('_pyrepl.fancy_termios',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.historical_reader',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.input',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/input.py',
   'PYMODULE'),
  ('_pyrepl.keymap',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/keymap.py',
   'PYMODULE'),
  ('_pyrepl.main',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/main.py',
   'PYMODULE'),
  ('_pyrepl.pager',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/pager.py',
   'PYMODULE'),
  ('_pyrepl.reader',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/reader.py',
   'PYMODULE'),
  ('_pyrepl.readline',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/readline.py',
   'PYMODULE'),
  ('_pyrepl.simple_interact',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.trace',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/trace.py',
   'PYMODULE'),
  ('_pyrepl.types',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/types.py',
   'PYMODULE'),
  ('_pyrepl.unix_console',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.utils',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/utils.py',
   'PYMODULE'),
  ('_pyrepl.windows_console',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pyrepl/windows_console.py',
   'PYMODULE'),
  ('_sitebuiltins',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_strptime.py',
   'PYMODULE'),
  ('_sysconfigdata__darwin_darwin',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_sysconfigdata__darwin_darwin.py',
   'PYMODULE'),
  ('_threading_local',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_threading_local.py',
   'PYMODULE'),
  ('api_client',
   '/Users/<USER>/PycharmProjects/PythonProject2/api_client.py',
   'PYMODULE'),
  ('api_result',
   '/Users/<USER>/PycharmProjects/PythonProject2/api_result.py',
   'PYMODULE'),
  ('argparse',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/argparse.py',
   'PYMODULE'),
  ('ast',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ast.py',
   'PYMODULE'),
  ('asyncio',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/locks.py',
   'PYMODULE'),
  ('asyncio.log',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/windows_utils.py',
   'PYMODULE'),
  ('attr',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/attr/__init__.py',
   'PYMODULE'),
  ('attr._cmp',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/attr/_cmp.py',
   'PYMODULE'),
  ('attr._compat',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/attr/_compat.py',
   'PYMODULE'),
  ('attr._config',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/attr/_config.py',
   'PYMODULE'),
  ('attr._funcs',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/attr/_funcs.py',
   'PYMODULE'),
  ('attr._make',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/attr/_make.py',
   'PYMODULE'),
  ('attr._next_gen',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/attr/_next_gen.py',
   'PYMODULE'),
  ('attr._version_info',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/attr/_version_info.py',
   'PYMODULE'),
  ('attr.converters',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/attr/converters.py',
   'PYMODULE'),
  ('attr.exceptions',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/attr/exceptions.py',
   'PYMODULE'),
  ('attr.filters',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/attr/filters.py',
   'PYMODULE'),
  ('attr.setters',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/attr/setters.py',
   'PYMODULE'),
  ('attr.validators',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/attr/validators.py',
   'PYMODULE'),
  ('attrs',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/attrs/__init__.py',
   'PYMODULE'),
  ('attrs.converters',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/attrs/converters.py',
   'PYMODULE'),
  ('attrs.exceptions',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/attrs/exceptions.py',
   'PYMODULE'),
  ('attrs.filters',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/attrs/filters.py',
   'PYMODULE'),
  ('attrs.setters',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/attrs/setters.py',
   'PYMODULE'),
  ('attrs.validators',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/attrs/validators.py',
   'PYMODULE'),
  ('backports',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/backports/__init__.py',
   'PYMODULE'),
  ('base64',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/base64.py',
   'PYMODULE'),
  ('bisect',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/bisect.py',
   'PYMODULE'),
  ('blinker',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/blinker/__init__.py',
   'PYMODULE'),
  ('blinker._saferef',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/blinker/_saferef.py',
   'PYMODULE'),
  ('blinker._utilities',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/blinker/_utilities.py',
   'PYMODULE'),
  ('blinker.base',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/blinker/base.py',
   'PYMODULE'),
  ('brotli',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/brotli.py',
   'PYMODULE'),
  ('bz2',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/bz2.py',
   'PYMODULE'),
  ('calendar',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/calendar.py',
   'PYMODULE'),
  ('certifi',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/certifi/__init__.py',
   'PYMODULE'),
  ('certifi.core',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/certifi/core.py',
   'PYMODULE'),
  ('cffi',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cffi/__init__.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cffi/_imp_emulation.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cffi/_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.api',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cffi/api.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cffi/cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cffi/commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cffi/cparser.py',
   'PYMODULE'),
  ('cffi.error',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cffi/error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cffi/ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cffi/lock.py',
   'PYMODULE'),
  ('cffi.model',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cffi/model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cffi/pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cffi/recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cffi/vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cffi/vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cffi/verifier.py',
   'PYMODULE'),
  ('charset_normalizer',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/charset_normalizer/__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/charset_normalizer/api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/charset_normalizer/cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/charset_normalizer/constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/charset_normalizer/legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/charset_normalizer/models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/charset_normalizer/utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/charset_normalizer/version.py',
   'PYMODULE'),
  ('code',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/code.py',
   'PYMODULE'),
  ('codeop',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/codeop.py',
   'PYMODULE'),
  ('concurrent',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/futures/__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/futures/_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/futures/process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/futures/thread.py',
   'PYMODULE'),
  ('config',
   '/Users/<USER>/PycharmProjects/PythonProject2/config.py',
   'PYMODULE'),
  ('configparser',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/configparser.py',
   'PYMODULE'),
  ('contextlib',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py',
   'PYMODULE'),
  ('contextvars',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextvars.py',
   'PYMODULE'),
  ('copy',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/copy.py',
   'PYMODULE'),
  ('cryptography',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/backends/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/backends/openssl/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/backends/openssl/backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/bindings/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/bindings/openssl/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/bindings/openssl/_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/bindings/openssl/binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/decrepit/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/decrepit/ciphers/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/decrepit/ciphers/algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/asymmetric/x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/ciphers/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/ciphers/algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/ciphers/base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/ciphers/modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/serialization/__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/serialization/base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/primitives/serialization/ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/x509/__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/x509/base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/x509/certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/x509/extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/x509/general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/x509/name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/x509/oid.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/x509/verification.py',
   'PYMODULE'),
  ('csv',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/csv.py',
   'PYMODULE'),
  ('ctypes',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/macholib/__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/macholib/dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/macholib/dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/macholib/framework.py',
   'PYMODULE'),
  ('ctypes.util',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ctypes/wintypes.py',
   'PYMODULE'),
  ('curses',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/curses/__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/curses/has_key.py',
   'PYMODULE'),
  ('dataclasses',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/dataclasses.py',
   'PYMODULE'),
  ('datetime',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/datetime.py',
   'PYMODULE'),
  ('decimal',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/decimal.py',
   'PYMODULE'),
  ('difflib',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/difflib.py',
   'PYMODULE'),
  ('dis',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/dis.py',
   'PYMODULE'),
  ('dotenv',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/dotenv/__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/dotenv/ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/dotenv/main.py',
   'PYMODULE'),
  ('dotenv.parser',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/dotenv/parser.py',
   'PYMODULE'),
  ('dotenv.variables',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/dotenv/variables.py',
   'PYMODULE'),
  ('email',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/base64mime.py',
   'PYMODULE'),
  ('email.charset',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/encoders.py',
   'PYMODULE'),
  ('email.errors',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/errors.py',
   'PYMODULE'),
  ('email.feedparser',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/feedparser.py',
   'PYMODULE'),
  ('email.generator',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/generator.py',
   'PYMODULE'),
  ('email.header',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/header.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/iterators.py',
   'PYMODULE'),
  ('email.message',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/message.py',
   'PYMODULE'),
  ('email.parser',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/parser.py',
   'PYMODULE'),
  ('email.policy',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/utils.py',
   'PYMODULE'),
  ('exceptions',
   '/Users/<USER>/PycharmProjects/PythonProject2/exceptions.py',
   'PYMODULE'),
  ('file_manager',
   '/Users/<USER>/PycharmProjects/PythonProject2/file_manager.py',
   'PYMODULE'),
  ('fnmatch',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/fnmatch.py',
   'PYMODULE'),
  ('fractions',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/fractions.py',
   'PYMODULE'),
  ('ftplib',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ftplib.py',
   'PYMODULE'),
  ('getopt',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/getopt.py',
   'PYMODULE'),
  ('getpass',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/getpass.py',
   'PYMODULE'),
  ('gettext',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/gettext.py',
   'PYMODULE'),
  ('glob',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/glob.py',
   'PYMODULE'),
  ('gzip',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/gzip.py',
   'PYMODULE'),
  ('h11',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/h11/__init__.py',
   'PYMODULE'),
  ('h11._abnf',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/h11/_abnf.py',
   'PYMODULE'),
  ('h11._connection',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/h11/_connection.py',
   'PYMODULE'),
  ('h11._events',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/h11/_events.py',
   'PYMODULE'),
  ('h11._headers',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/h11/_headers.py',
   'PYMODULE'),
  ('h11._readers',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/h11/_readers.py',
   'PYMODULE'),
  ('h11._receivebuffer',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/h11/_receivebuffer.py',
   'PYMODULE'),
  ('h11._state',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/h11/_state.py',
   'PYMODULE'),
  ('h11._util',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/h11/_util.py',
   'PYMODULE'),
  ('h11._version',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/h11/_version.py',
   'PYMODULE'),
  ('h11._writers',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/h11/_writers.py',
   'PYMODULE'),
  ('h2',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/h2/__init__.py',
   'PYMODULE'),
  ('h2.config',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/h2/config.py',
   'PYMODULE'),
  ('h2.connection',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/h2/connection.py',
   'PYMODULE'),
  ('h2.errors',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/h2/errors.py',
   'PYMODULE'),
  ('h2.events',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/h2/events.py',
   'PYMODULE'),
  ('h2.exceptions',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/h2/exceptions.py',
   'PYMODULE'),
  ('h2.frame_buffer',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/h2/frame_buffer.py',
   'PYMODULE'),
  ('h2.settings',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/h2/settings.py',
   'PYMODULE'),
  ('h2.stream',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/h2/stream.py',
   'PYMODULE'),
  ('h2.utilities',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/h2/utilities.py',
   'PYMODULE'),
  ('h2.windows',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/h2/windows.py',
   'PYMODULE'),
  ('hashlib',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/hashlib.py',
   'PYMODULE'),
  ('hmac',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/hmac.py',
   'PYMODULE'),
  ('hpack',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/hpack/__init__.py',
   'PYMODULE'),
  ('hpack.exceptions',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/hpack/exceptions.py',
   'PYMODULE'),
  ('hpack.hpack',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/hpack/hpack.py',
   'PYMODULE'),
  ('hpack.huffman',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/hpack/huffman.py',
   'PYMODULE'),
  ('hpack.huffman_constants',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/hpack/huffman_constants.py',
   'PYMODULE'),
  ('hpack.huffman_table',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/hpack/huffman_table.py',
   'PYMODULE'),
  ('hpack.struct',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/hpack/struct.py',
   'PYMODULE'),
  ('hpack.table',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/hpack/table.py',
   'PYMODULE'),
  ('html',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/html/__init__.py',
   'PYMODULE'),
  ('html.entities',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/html/entities.py',
   'PYMODULE'),
  ('http',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/__init__.py',
   'PYMODULE'),
  ('http.client',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/client.py',
   'PYMODULE'),
  ('http.cookiejar',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/cookies.py',
   'PYMODULE'),
  ('http.server',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/server.py',
   'PYMODULE'),
  ('hyperframe',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/hyperframe/__init__.py',
   'PYMODULE'),
  ('hyperframe.exceptions',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/hyperframe/exceptions.py',
   'PYMODULE'),
  ('hyperframe.flags',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/hyperframe/flags.py',
   'PYMODULE'),
  ('hyperframe.frame',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/hyperframe/frame.py',
   'PYMODULE'),
  ('idna',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/idna/__init__.py',
   'PYMODULE'),
  ('idna.core',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/idna/core.py',
   'PYMODULE'),
  ('idna.idnadata',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/idna/idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/idna/intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/idna/package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/idna/uts46data.py',
   'PYMODULE'),
  ('importlib',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_text.py',
   'PYMODULE'),
  ('importlib.readers',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/readers.py',
   'PYMODULE'),
  ('importlib.resources',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/readers.py',
   'PYMODULE'),
  ('importlib.util',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/util.py',
   'PYMODULE'),
  ('inspect',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/inspect.py',
   'PYMODULE'),
  ('ipaddress',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ipaddress.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('json',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/__init__.py',
   'PYMODULE'),
  ('json.decoder',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/decoder.py',
   'PYMODULE'),
  ('json.encoder',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/encoder.py',
   'PYMODULE'),
  ('json.scanner',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/scanner.py',
   'PYMODULE'),
  ('kaitaistruct',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/kaitaistruct.py',
   'PYMODULE'),
  ('logging',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/logging/__init__.py',
   'PYMODULE'),
  ('lzma',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lzma.py',
   'PYMODULE'),
  ('mimetypes',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/dummy/__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/dummy/connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/util.py',
   'PYMODULE'),
  ('netrc',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/netrc.py',
   'PYMODULE'),
  ('nturl2path',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/nturl2path.py',
   'PYMODULE'),
  ('numbers',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/numbers.py',
   'PYMODULE'),
  ('opcode',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/opcode.py',
   'PYMODULE'),
  ('outcome',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/outcome/__init__.py',
   'PYMODULE'),
  ('outcome._impl',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/outcome/_impl.py',
   'PYMODULE'),
  ('outcome._util',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/outcome/_util.py',
   'PYMODULE'),
  ('outcome._version',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/outcome/_version.py',
   'PYMODULE'),
  ('packaging',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/packaging/__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/packaging/_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/packaging/_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/packaging/_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/packaging/_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/packaging/_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/packaging/_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/packaging/licenses/__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/packaging/licenses/_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/packaging/markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/packaging/metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/packaging/requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/packaging/specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/packaging/tags.py',
   'PYMODULE'),
  ('packaging.utils',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/packaging/utils.py',
   'PYMODULE'),
  ('packaging.version',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/packaging/version.py',
   'PYMODULE'),
  ('path_manager',
   '/Users/<USER>/PycharmProjects/PythonProject2/path_manager.py',
   'PYMODULE'),
  ('pathlib',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pathlib/__init__.py',
   'PYMODULE'),
  ('pathlib._abc',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pathlib/_abc.py',
   'PYMODULE'),
  ('pathlib._local',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pathlib/_local.py',
   'PYMODULE'),
  ('pickle',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pickle.py',
   'PYMODULE'),
  ('pkg_resources',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pkg_resources/__init__.py',
   'PYMODULE'),
  ('pkgutil',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pkgutil.py',
   'PYMODULE'),
  ('platform',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/platform.py',
   'PYMODULE'),
  ('plistlib',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/plistlib.py',
   'PYMODULE'),
  ('pprint',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pprint.py',
   'PYMODULE'),
  ('py_compile',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/py_compile.py',
   'PYMODULE'),
  ('pyasn1',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyasn1/__init__.py',
   'PYMODULE'),
  ('pyasn1.codec',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyasn1/codec/__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.ber',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyasn1/codec/ber/__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.ber.decoder',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyasn1/codec/ber/decoder.py',
   'PYMODULE'),
  ('pyasn1.codec.ber.eoo',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyasn1/codec/ber/eoo.py',
   'PYMODULE'),
  ('pyasn1.codec.cer',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyasn1/codec/cer/__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.cer.decoder',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyasn1/codec/cer/decoder.py',
   'PYMODULE'),
  ('pyasn1.codec.der',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyasn1/codec/der/__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.der.decoder',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyasn1/codec/der/decoder.py',
   'PYMODULE'),
  ('pyasn1.codec.streaming',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyasn1/codec/streaming.py',
   'PYMODULE'),
  ('pyasn1.compat',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyasn1/compat/__init__.py',
   'PYMODULE'),
  ('pyasn1.compat.integer',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyasn1/compat/integer.py',
   'PYMODULE'),
  ('pyasn1.debug',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyasn1/debug.py',
   'PYMODULE'),
  ('pyasn1.error',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyasn1/error.py',
   'PYMODULE'),
  ('pyasn1.type',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyasn1/type/__init__.py',
   'PYMODULE'),
  ('pyasn1.type.base',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyasn1/type/base.py',
   'PYMODULE'),
  ('pyasn1.type.char',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyasn1/type/char.py',
   'PYMODULE'),
  ('pyasn1.type.constraint',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyasn1/type/constraint.py',
   'PYMODULE'),
  ('pyasn1.type.error',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyasn1/type/error.py',
   'PYMODULE'),
  ('pyasn1.type.namedtype',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyasn1/type/namedtype.py',
   'PYMODULE'),
  ('pyasn1.type.namedval',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyasn1/type/namedval.py',
   'PYMODULE'),
  ('pyasn1.type.tag',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyasn1/type/tag.py',
   'PYMODULE'),
  ('pyasn1.type.tagmap',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyasn1/type/tagmap.py',
   'PYMODULE'),
  ('pyasn1.type.univ',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyasn1/type/univ.py',
   'PYMODULE'),
  ('pyasn1.type.useful',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyasn1/type/useful.py',
   'PYMODULE'),
  ('pycparser',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pycparser/__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pycparser/ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pycparser/c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pycparser/c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pycparser/c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pycparser/lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pycparser/ply/__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pycparser/ply/lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pycparser/ply/yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pycparser/plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pycparser/yacctab.py',
   'PYMODULE'),
  ('pydoc',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pydoc_data/__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pydoc_data/topics.py',
   'PYMODULE'),
  ('pyparsing',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyparsing/__init__.py',
   'PYMODULE'),
  ('pyparsing.actions',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyparsing/actions.py',
   'PYMODULE'),
  ('pyparsing.common',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyparsing/common.py',
   'PYMODULE'),
  ('pyparsing.core',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyparsing/core.py',
   'PYMODULE'),
  ('pyparsing.diagram',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyparsing/diagram/__init__.py',
   'PYMODULE'),
  ('pyparsing.exceptions',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyparsing/exceptions.py',
   'PYMODULE'),
  ('pyparsing.helpers',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyparsing/helpers.py',
   'PYMODULE'),
  ('pyparsing.results',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyparsing/results.py',
   'PYMODULE'),
  ('pyparsing.testing',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyparsing/testing.py',
   'PYMODULE'),
  ('pyparsing.unicode',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyparsing/unicode.py',
   'PYMODULE'),
  ('pyparsing.util',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/pyparsing/util.py',
   'PYMODULE'),
  ('queue',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/queue.py',
   'PYMODULE'),
  ('quopri',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/quopri.py',
   'PYMODULE'),
  ('random',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/random.py',
   'PYMODULE'),
  ('requests',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/requests/__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/requests/__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/requests/_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/requests/adapters.py',
   'PYMODULE'),
  ('requests.api',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/requests/api.py',
   'PYMODULE'),
  ('requests.auth',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/requests/auth.py',
   'PYMODULE'),
  ('requests.certs',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/requests/certs.py',
   'PYMODULE'),
  ('requests.compat',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/requests/compat.py',
   'PYMODULE'),
  ('requests.cookies',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/requests/cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/requests/exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/requests/hooks.py',
   'PYMODULE'),
  ('requests.models',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/requests/models.py',
   'PYMODULE'),
  ('requests.packages',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/requests/packages.py',
   'PYMODULE'),
  ('requests.sessions',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/requests/sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/requests/status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/requests/structures.py',
   'PYMODULE'),
  ('requests.utils',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/requests/utils.py',
   'PYMODULE'),
  ('rlcompleter',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/rlcompleter.py',
   'PYMODULE'),
  ('runpy',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/runpy.py',
   'PYMODULE'),
  ('secrets',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/secrets.py',
   'PYMODULE'),
  ('selectors',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/selectors.py',
   'PYMODULE'),
  ('selenium',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/__init__.py',
   'PYMODULE'),
  ('selenium.common',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/common/__init__.py',
   'PYMODULE'),
  ('selenium.common.exceptions',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/common/exceptions.py',
   'PYMODULE'),
  ('selenium.types',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/types.py',
   'PYMODULE'),
  ('selenium.webdriver',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/chrome/__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.options',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/chrome/options.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.remote_connection',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/chrome/remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.service',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/chrome/service.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.webdriver',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/chrome/webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/chromium/__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.options',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/chromium/options.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.remote_connection',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/chromium/remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.service',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/chromium/service.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.webdriver',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/chromium/webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.common',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.action_chains',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/action_chains.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/actions/__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.action_builder',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/actions/action_builder.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.input_device',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/actions/input_device.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.interaction',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/actions/interaction.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.key_actions',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/actions/key_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.key_input',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/actions/key_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.mouse_button',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/actions/mouse_button.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.pointer_actions',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/actions/pointer_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.pointer_input',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/actions/pointer_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.wheel_actions',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/actions/wheel_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.wheel_input',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/actions/wheel_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.alert',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/alert.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/bidi/__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.browser',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/bidi/browser.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.browsing_context',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/bidi/browsing_context.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.common',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/bidi/common.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.log',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/bidi/log.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.network',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/bidi/network.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.script',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/bidi/script.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.session',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/bidi/session.py',
   'PYMODULE'),
  ('selenium.webdriver.common.by',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/by.py',
   'PYMODULE'),
  ('selenium.webdriver.common.desired_capabilities',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/desired_capabilities.py',
   'PYMODULE'),
  ('selenium.webdriver.common.driver_finder',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/driver_finder.py',
   'PYMODULE'),
  ('selenium.webdriver.common.fedcm',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/fedcm/__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.fedcm.account',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/fedcm/account.py',
   'PYMODULE'),
  ('selenium.webdriver.common.fedcm.dialog',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/fedcm/dialog.py',
   'PYMODULE'),
  ('selenium.webdriver.common.keys',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/keys.py',
   'PYMODULE'),
  ('selenium.webdriver.common.options',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/options.py',
   'PYMODULE'),
  ('selenium.webdriver.common.print_page_options',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/print_page_options.py',
   'PYMODULE'),
  ('selenium.webdriver.common.proxy',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/proxy.py',
   'PYMODULE'),
  ('selenium.webdriver.common.selenium_manager',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/selenium_manager.py',
   'PYMODULE'),
  ('selenium.webdriver.common.service',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/service.py',
   'PYMODULE'),
  ('selenium.webdriver.common.timeouts',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/timeouts.py',
   'PYMODULE'),
  ('selenium.webdriver.common.utils',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/utils.py',
   'PYMODULE'),
  ('selenium.webdriver.common.virtual_authenticator',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/virtual_authenticator.py',
   'PYMODULE'),
  ('selenium.webdriver.edge',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/edge/__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.options',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/edge/options.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.remote_connection',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/edge/remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.service',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/edge/service.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.webdriver',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/edge/webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/firefox/__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.firefox_binary',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/firefox/firefox_binary.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.firefox_profile',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/firefox/firefox_profile.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.options',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/firefox/options.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.remote_connection',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/firefox/remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.service',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/firefox/service.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.webdriver',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/firefox/webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.ie',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/ie/__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.options',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/ie/options.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.service',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/ie/service.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.webdriver',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/ie/webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.remote',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/remote/__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.bidi_connection',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/remote/bidi_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.client_config',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/remote/client_config.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.command',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/remote/command.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.errorhandler',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/remote/errorhandler.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.fedcm',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/remote/fedcm.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.file_detector',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/remote/file_detector.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.locator_converter',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/remote/locator_converter.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.mobile',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/remote/mobile.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.remote_connection',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/remote/remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.script_key',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/remote/script_key.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.shadowroot',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/remote/shadowroot.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.switch_to',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/remote/switch_to.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.utils',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/remote/utils.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.webdriver',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/remote/webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.webelement',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/remote/webelement.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.websocket_connection',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/remote/websocket_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.safari',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/safari/__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.options',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/safari/options.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.remote_connection',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/safari/remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.service',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/safari/service.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.webdriver',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/safari/webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.support',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/support/__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.support.expected_conditions',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/support/expected_conditions.py',
   'PYMODULE'),
  ('selenium.webdriver.support.relative_locator',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/support/relative_locator.py',
   'PYMODULE'),
  ('selenium.webdriver.support.select',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/support/select.py',
   'PYMODULE'),
  ('selenium.webdriver.support.ui',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/support/ui.py',
   'PYMODULE'),
  ('selenium.webdriver.support.wait',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/support/wait.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/webkitgtk/__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.options',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/webkitgtk/options.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.service',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/webkitgtk/service.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.webdriver',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/webkitgtk/webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/wpewebkit/__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.options',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/wpewebkit/options.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.service',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/wpewebkit/service.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.webdriver',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/wpewebkit/webdriver.py',
   'PYMODULE'),
  ('seleniumwire',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/__init__.py',
   'PYMODULE'),
  ('seleniumwire.backend',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/backend.py',
   'PYMODULE'),
  ('seleniumwire.handler',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/handler.py',
   'PYMODULE'),
  ('seleniumwire.har',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/har.py',
   'PYMODULE'),
  ('seleniumwire.inspect',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/inspect.py',
   'PYMODULE'),
  ('seleniumwire.modifier',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/modifier.py',
   'PYMODULE'),
  ('seleniumwire.request',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/request.py',
   'PYMODULE'),
  ('seleniumwire.server',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/server.py',
   'PYMODULE'),
  ('seleniumwire.storage',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/storage.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/__init__.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/__init__.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.addonmanager',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/addonmanager.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.addons',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/addons/__init__.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.addons.core',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/addons/core.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.addons.streambodies',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/addons/streambodies.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.addons.upstream_auth',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/addons/upstream_auth.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.certs',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/certs.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.command',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/command.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.command_lexer',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/command_lexer.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.connections',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/connections.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.contrib',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/contrib/__init__.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.contrib.kaitaistruct',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/contrib/kaitaistruct/__init__.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.contrib.kaitaistruct.tls_client_hello',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/contrib/kaitaistruct/tls_client_hello.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.controller',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/controller.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.coretypes',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/coretypes/__init__.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.coretypes.basethread',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/coretypes/basethread.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.coretypes.bidi',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/coretypes/bidi.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.coretypes.multidict',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/coretypes/multidict.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.coretypes.serializable',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/coretypes/serializable.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.ctx',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/ctx.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.eventsequence',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/eventsequence.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.exceptions',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/exceptions.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.flow',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/flow.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.http',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/http.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.log',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/log.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.master',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/master.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.net',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/net/__init__.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.net.check',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/net/check.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.net.http',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/net/http/__init__.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.net.http.cookies',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/net/http/cookies.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.net.http.encoding',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/net/http/encoding.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.net.http.headers',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/net/http/headers.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.net.http.http1',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/net/http/http1/__init__.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.net.http.http1.assemble',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/net/http/http1/assemble.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.net.http.http1.read',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/net/http/http1/read.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.net.http.http2',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/net/http/http2/__init__.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.net.http.http2.framereader',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/net/http/http2/framereader.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.net.http.http2.utils',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/net/http/http2/utils.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.net.http.message',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/net/http/message.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.net.http.multipart',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/net/http/multipart.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.net.http.request',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/net/http/request.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.net.http.response',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/net/http/response.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.net.http.status_codes',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/net/http/status_codes.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.net.http.url',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/net/http/url.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.net.server_spec',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/net/server_spec.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.net.tcp',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/net/tcp.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.net.tls',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/net/tls.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.net.websockets',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/net/websockets/__init__.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.net.websockets.frame',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/net/websockets/frame.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.net.websockets.masker',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/net/websockets/masker.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.net.websockets.utils',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/net/websockets/utils.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.options',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/options.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.optmanager',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/optmanager.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.platform',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/platform/__init__.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.platform.linux',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/platform/linux.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.platform.openbsd',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/platform/openbsd.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.platform.osx',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/platform/osx.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.platform.pf',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/platform/pf.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.platform.windows',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/platform/windows.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.server',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/server/__init__.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.server.config',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/server/config.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.server.modes',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/server/modes/__init__.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.server.modes.http_proxy',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/server/modes/http_proxy.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.server.modes.socks_proxy',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/server/modes/socks_proxy.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.server.protocol',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/server/protocol/__init__.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.server.protocol.base',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/server/protocol/base.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.server.protocol.http',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/server/protocol/http.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.server.protocol.http1',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/server/protocol/http1.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.server.protocol.http2',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/server/protocol/http2.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.server.protocol.rawtcp',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/server/protocol/rawtcp.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.server.protocol.tls',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/server/protocol/tls.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.server.protocol.websocket',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/server/protocol/websocket.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.server.root_context',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/server/root_context.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.server.server',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/server/server.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.stateobject',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/stateobject.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.tcp',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/tcp.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.types',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/types.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.utils',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/utils/__init__.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.utils.bits',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/utils/bits.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.utils.human',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/utils/human.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.utils.strutils',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/utils/strutils.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.utils.typecheck',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/utils/typecheck.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.version',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/version.py',
   'PYMODULE'),
  ('seleniumwire.thirdparty.mitmproxy.websocket',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/thirdparty/mitmproxy/websocket.py',
   'PYMODULE'),
  ('seleniumwire.utils',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/utils.py',
   'PYMODULE'),
  ('seleniumwire.webdriver',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/webdriver.py',
   'PYMODULE'),
  ('setuptools',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/command/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/command/bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/command/build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/command/build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/command/sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/compat/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/compat/numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/compat/py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/compilers/C/base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/compilers/C/errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/compilers/C/msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_distutils/versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/backports/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/compat/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/compat/py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/compat/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/compat/py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/compat/py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/jaraco/context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/jaraco/functools/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/more_itertools/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/more_itertools/more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/more_itertools/recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/platformdirs/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/platformdirs/android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/platformdirs/api.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/platformdirs/macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/platformdirs/unix.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/platformdirs/version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/platformdirs/windows.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/tomli/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/tomli/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/tomli/_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/tomli/_types.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/zipp/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/zipp/compat/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/zipp/compat/py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/zipp/glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/command/__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/command/_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/command/bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/command/bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/command/build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/command/egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/command/sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/command/setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/compat/__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/compat/py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/compat/py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/compat/py39.py',
   'PYMODULE'),
  ('setuptools.config',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/config/__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/config/_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/config/expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/config/pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/config/setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/windows_support.py',
   'PYMODULE'),
  ('shlex',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/shlex.py',
   'PYMODULE'),
  ('shutil',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/shutil.py',
   'PYMODULE'),
  ('signal',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/signal.py',
   'PYMODULE'),
  ('site',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site.py',
   'PYMODULE'),
  ('sniffio',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/sniffio/__init__.py',
   'PYMODULE'),
  ('sniffio._impl',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/sniffio/_impl.py',
   'PYMODULE'),
  ('sniffio._version',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/sniffio/_version.py',
   'PYMODULE'),
  ('socket',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socket.py',
   'PYMODULE'),
  ('socketserver',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socketserver.py',
   'PYMODULE'),
  ('socks',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/socks.py',
   'PYMODULE'),
  ('sortedcontainers',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/sortedcontainers/__init__.py',
   'PYMODULE'),
  ('sortedcontainers.sorteddict',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/sortedcontainers/sorteddict.py',
   'PYMODULE'),
  ('sortedcontainers.sortedlist',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/sortedcontainers/sortedlist.py',
   'PYMODULE'),
  ('sortedcontainers.sortedset',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/sortedcontainers/sortedset.py',
   'PYMODULE'),
  ('ssl',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ssl.py',
   'PYMODULE'),
  ('statistics',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/statistics.py',
   'PYMODULE'),
  ('string',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/string.py',
   'PYMODULE'),
  ('stringprep',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/stringprep.py',
   'PYMODULE'),
  ('subprocess',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/sysconfig/__init__.py',
   'PYMODULE'),
  ('tarfile',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tarfile.py',
   'PYMODULE'),
  ('tempfile',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tempfile.py',
   'PYMODULE'),
  ('textwrap',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/textwrap.py',
   'PYMODULE'),
  ('threading',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/threading.py',
   'PYMODULE'),
  ('token',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/token.py',
   'PYMODULE'),
  ('tokenize',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tokenize.py',
   'PYMODULE'),
  ('tomllib',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tomllib/__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tomllib/_parser.py',
   'PYMODULE'),
  ('tomllib._re',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tomllib/_re.py',
   'PYMODULE'),
  ('tomllib._types',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tomllib/_types.py',
   'PYMODULE'),
  ('tqdm',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/tqdm/__init__.py',
   'PYMODULE'),
  ('tqdm._dist_ver',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/tqdm/_dist_ver.py',
   'PYMODULE'),
  ('tqdm._monitor',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/tqdm/_monitor.py',
   'PYMODULE'),
  ('tqdm._tqdm_pandas',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/tqdm/_tqdm_pandas.py',
   'PYMODULE'),
  ('tqdm.cli',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/tqdm/cli.py',
   'PYMODULE'),
  ('tqdm.gui',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/tqdm/gui.py',
   'PYMODULE'),
  ('tqdm.notebook',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/tqdm/notebook.py',
   'PYMODULE'),
  ('tqdm.std',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/tqdm/std.py',
   'PYMODULE'),
  ('tqdm.utils',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/tqdm/utils.py',
   'PYMODULE'),
  ('tqdm.version',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/tqdm/version.py',
   'PYMODULE'),
  ('tracemalloc',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tracemalloc.py',
   'PYMODULE'),
  ('trio',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/__init__.py',
   'PYMODULE'),
  ('trio._abc',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_abc.py',
   'PYMODULE'),
  ('trio._channel',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_channel.py',
   'PYMODULE'),
  ('trio._core',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_core/__init__.py',
   'PYMODULE'),
  ('trio._core._asyncgens',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_core/_asyncgens.py',
   'PYMODULE'),
  ('trio._core._concat_tb',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_core/_concat_tb.py',
   'PYMODULE'),
  ('trio._core._entry_queue',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_core/_entry_queue.py',
   'PYMODULE'),
  ('trio._core._exceptions',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_core/_exceptions.py',
   'PYMODULE'),
  ('trio._core._generated_instrumentation',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_core/_generated_instrumentation.py',
   'PYMODULE'),
  ('trio._core._generated_io_epoll',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_core/_generated_io_epoll.py',
   'PYMODULE'),
  ('trio._core._generated_io_kqueue',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_core/_generated_io_kqueue.py',
   'PYMODULE'),
  ('trio._core._generated_io_windows',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_core/_generated_io_windows.py',
   'PYMODULE'),
  ('trio._core._generated_run',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_core/_generated_run.py',
   'PYMODULE'),
  ('trio._core._instrumentation',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_core/_instrumentation.py',
   'PYMODULE'),
  ('trio._core._io_common',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_core/_io_common.py',
   'PYMODULE'),
  ('trio._core._io_epoll',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_core/_io_epoll.py',
   'PYMODULE'),
  ('trio._core._io_kqueue',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_core/_io_kqueue.py',
   'PYMODULE'),
  ('trio._core._io_windows',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_core/_io_windows.py',
   'PYMODULE'),
  ('trio._core._ki',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_core/_ki.py',
   'PYMODULE'),
  ('trio._core._local',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_core/_local.py',
   'PYMODULE'),
  ('trio._core._mock_clock',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_core/_mock_clock.py',
   'PYMODULE'),
  ('trio._core._parking_lot',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_core/_parking_lot.py',
   'PYMODULE'),
  ('trio._core._run',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_core/_run.py',
   'PYMODULE'),
  ('trio._core._run_context',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_core/_run_context.py',
   'PYMODULE'),
  ('trio._core._thread_cache',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_core/_thread_cache.py',
   'PYMODULE'),
  ('trio._core._traps',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_core/_traps.py',
   'PYMODULE'),
  ('trio._core._unbounded_queue',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_core/_unbounded_queue.py',
   'PYMODULE'),
  ('trio._core._wakeup_socketpair',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_core/_wakeup_socketpair.py',
   'PYMODULE'),
  ('trio._core._windows_cffi',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_core/_windows_cffi.py',
   'PYMODULE'),
  ('trio._deprecate',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_deprecate.py',
   'PYMODULE'),
  ('trio._dtls',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_dtls.py',
   'PYMODULE'),
  ('trio._file_io',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_file_io.py',
   'PYMODULE'),
  ('trio._highlevel_generic',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_highlevel_generic.py',
   'PYMODULE'),
  ('trio._highlevel_open_tcp_listeners',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_highlevel_open_tcp_listeners.py',
   'PYMODULE'),
  ('trio._highlevel_open_tcp_stream',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_highlevel_open_tcp_stream.py',
   'PYMODULE'),
  ('trio._highlevel_open_unix_stream',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_highlevel_open_unix_stream.py',
   'PYMODULE'),
  ('trio._highlevel_serve_listeners',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_highlevel_serve_listeners.py',
   'PYMODULE'),
  ('trio._highlevel_socket',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_highlevel_socket.py',
   'PYMODULE'),
  ('trio._highlevel_ssl_helpers',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_highlevel_ssl_helpers.py',
   'PYMODULE'),
  ('trio._path',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_path.py',
   'PYMODULE'),
  ('trio._signals',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_signals.py',
   'PYMODULE'),
  ('trio._socket',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_socket.py',
   'PYMODULE'),
  ('trio._ssl',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_ssl.py',
   'PYMODULE'),
  ('trio._subprocess',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_subprocess.py',
   'PYMODULE'),
  ('trio._subprocess_platform',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_subprocess_platform/__init__.py',
   'PYMODULE'),
  ('trio._subprocess_platform.kqueue',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_subprocess_platform/kqueue.py',
   'PYMODULE'),
  ('trio._subprocess_platform.waitid',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_subprocess_platform/waitid.py',
   'PYMODULE'),
  ('trio._subprocess_platform.windows',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_subprocess_platform/windows.py',
   'PYMODULE'),
  ('trio._sync',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_sync.py',
   'PYMODULE'),
  ('trio._threads',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_threads.py',
   'PYMODULE'),
  ('trio._timeouts',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_timeouts.py',
   'PYMODULE'),
  ('trio._unix_pipes',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_unix_pipes.py',
   'PYMODULE'),
  ('trio._util',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_util.py',
   'PYMODULE'),
  ('trio._version',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_version.py',
   'PYMODULE'),
  ('trio._wait_for_object',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_wait_for_object.py',
   'PYMODULE'),
  ('trio._windows_pipes',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/_windows_pipes.py',
   'PYMODULE'),
  ('trio.abc',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/abc.py',
   'PYMODULE'),
  ('trio.from_thread',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/from_thread.py',
   'PYMODULE'),
  ('trio.lowlevel',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/lowlevel.py',
   'PYMODULE'),
  ('trio.socket',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/socket.py',
   'PYMODULE'),
  ('trio.testing',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/testing/__init__.py',
   'PYMODULE'),
  ('trio.testing._check_streams',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/testing/_check_streams.py',
   'PYMODULE'),
  ('trio.testing._checkpoints',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/testing/_checkpoints.py',
   'PYMODULE'),
  ('trio.testing._memory_streams',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/testing/_memory_streams.py',
   'PYMODULE'),
  ('trio.testing._network',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/testing/_network.py',
   'PYMODULE'),
  ('trio.testing._raises_group',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/testing/_raises_group.py',
   'PYMODULE'),
  ('trio.testing._sequencer',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/testing/_sequencer.py',
   'PYMODULE'),
  ('trio.testing._trio_test',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/testing/_trio_test.py',
   'PYMODULE'),
  ('trio.to_thread',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio/to_thread.py',
   'PYMODULE'),
  ('trio_websocket',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio_websocket/__init__.py',
   'PYMODULE'),
  ('trio_websocket._impl',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio_websocket/_impl.py',
   'PYMODULE'),
  ('trio_websocket._version',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio_websocket/_version.py',
   'PYMODULE'),
  ('tty',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tty.py',
   'PYMODULE'),
  ('typing',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/typing.py',
   'PYMODULE'),
  ('typing_extensions',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/__init__.py',
   'PYMODULE'),
  ('unittest._log',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/async_case.py',
   'PYMODULE'),
  ('unittest.case',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/case.py',
   'PYMODULE'),
  ('unittest.loader',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/loader.py',
   'PYMODULE'),
  ('unittest.main',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/main.py',
   'PYMODULE'),
  ('unittest.mock',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/mock.py',
   'PYMODULE'),
  ('unittest.result',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/result.py',
   'PYMODULE'),
  ('unittest.runner',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/runner.py',
   'PYMODULE'),
  ('unittest.signals',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/signals.py',
   'PYMODULE'),
  ('unittest.suite',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/suite.py',
   'PYMODULE'),
  ('unittest.util',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/unittest/util.py',
   'PYMODULE'),
  ('urllib',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/urllib/__init__.py',
   'PYMODULE'),
  ('urllib.error',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/urllib/error.py',
   'PYMODULE'),
  ('urllib.parse',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/urllib/parse.py',
   'PYMODULE'),
  ('urllib.request',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/urllib/request.py',
   'PYMODULE'),
  ('urllib.response',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/urllib/response.py',
   'PYMODULE'),
  ('urllib3',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/__init__.py',
   'PYMODULE'),
  ('urllib3._collections',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/_collections.py',
   'PYMODULE'),
  ('urllib3._version',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/contrib/__init__.py',
   'PYMODULE'),
  ('urllib3.contrib._appengine_environ',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/contrib/_appengine_environ.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/contrib/pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/contrib/socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/filepost.py',
   'PYMODULE'),
  ('urllib3.packages',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/packages/__init__.py',
   'PYMODULE'),
  ('urllib3.packages.backports',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/packages/backports/__init__.py',
   'PYMODULE'),
  ('urllib3.packages.backports.makefile',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/packages/backports/makefile.py',
   'PYMODULE'),
  ('urllib3.packages.backports.weakref_finalize',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/packages/backports/weakref_finalize.py',
   'PYMODULE'),
  ('urllib3.packages.six',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/packages/six.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/poolmanager.py',
   'PYMODULE'),
  ('urllib3.request',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/request.py',
   'PYMODULE'),
  ('urllib3.response',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/response.py',
   'PYMODULE'),
  ('urllib3.util',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/util/__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/util/connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/util/proxy.py',
   'PYMODULE'),
  ('urllib3.util.queue',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/util/queue.py',
   'PYMODULE'),
  ('urllib3.util.request',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/util/request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/util/response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/util/retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/util/ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/util/ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/util/ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/util/timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/util/url.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/urllib3/util/wait.py',
   'PYMODULE'),
  ('user_interface',
   '/Users/<USER>/PycharmProjects/PythonProject2/user_interface.py',
   'PYMODULE'),
  ('utils',
   '/Users/<USER>/PycharmProjects/PythonProject2/utils.py',
   'PYMODULE'),
  ('uuid',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/uuid.py',
   'PYMODULE'),
  ('webbrowser',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/webbrowser.py',
   'PYMODULE'),
  ('webdriver_manager',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/webdriver_manager/__init__.py',
   'PYMODULE'),
  ('webdriver_manager.chrome',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/webdriver_manager/chrome.py',
   'PYMODULE'),
  ('webdriver_manager.core',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/webdriver_manager/core/__init__.py',
   'PYMODULE'),
  ('webdriver_manager.core.archive',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/webdriver_manager/core/archive.py',
   'PYMODULE'),
  ('webdriver_manager.core.config',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/webdriver_manager/core/config.py',
   'PYMODULE'),
  ('webdriver_manager.core.constants',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/webdriver_manager/core/constants.py',
   'PYMODULE'),
  ('webdriver_manager.core.download_manager',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/webdriver_manager/core/download_manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.driver',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/webdriver_manager/core/driver.py',
   'PYMODULE'),
  ('webdriver_manager.core.driver_cache',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/webdriver_manager/core/driver_cache.py',
   'PYMODULE'),
  ('webdriver_manager.core.file_manager',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/webdriver_manager/core/file_manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.http',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/webdriver_manager/core/http.py',
   'PYMODULE'),
  ('webdriver_manager.core.logger',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/webdriver_manager/core/logger.py',
   'PYMODULE'),
  ('webdriver_manager.core.manager',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/webdriver_manager/core/manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.os_manager',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/webdriver_manager/core/os_manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.utils',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/webdriver_manager/core/utils.py',
   'PYMODULE'),
  ('webdriver_manager.drivers',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/webdriver_manager/drivers/__init__.py',
   'PYMODULE'),
  ('webdriver_manager.drivers.chrome',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/webdriver_manager/drivers/chrome.py',
   'PYMODULE'),
  ('websocket',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/websocket/__init__.py',
   'PYMODULE'),
  ('websocket._abnf',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/websocket/_abnf.py',
   'PYMODULE'),
  ('websocket._app',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/websocket/_app.py',
   'PYMODULE'),
  ('websocket._cookiejar',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/websocket/_cookiejar.py',
   'PYMODULE'),
  ('websocket._core',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/websocket/_core.py',
   'PYMODULE'),
  ('websocket._exceptions',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/websocket/_exceptions.py',
   'PYMODULE'),
  ('websocket._handshake',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/websocket/_handshake.py',
   'PYMODULE'),
  ('websocket._http',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/websocket/_http.py',
   'PYMODULE'),
  ('websocket._logging',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/websocket/_logging.py',
   'PYMODULE'),
  ('websocket._socket',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/websocket/_socket.py',
   'PYMODULE'),
  ('websocket._ssl_compat',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/websocket/_ssl_compat.py',
   'PYMODULE'),
  ('websocket._url',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/websocket/_url.py',
   'PYMODULE'),
  ('websocket._utils',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/websocket/_utils.py',
   'PYMODULE'),
  ('wsproto',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/wsproto/__init__.py',
   'PYMODULE'),
  ('wsproto.connection',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/wsproto/connection.py',
   'PYMODULE'),
  ('wsproto.events',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/wsproto/events.py',
   'PYMODULE'),
  ('wsproto.extensions',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/wsproto/extensions.py',
   'PYMODULE'),
  ('wsproto.frame_protocol',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/wsproto/frame_protocol.py',
   'PYMODULE'),
  ('wsproto.handshake',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/wsproto/handshake.py',
   'PYMODULE'),
  ('wsproto.typing',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/wsproto/typing.py',
   'PYMODULE'),
  ('wsproto.utilities',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/wsproto/utilities.py',
   'PYMODULE'),
  ('xml',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/__init__.py',
   'PYMODULE'),
  ('xml.dom',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/dom/__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/dom/NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/dom/domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/dom/expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/dom/minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/dom/minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/dom/pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/dom/xmlbuilder.py',
   'PYMODULE'),
  ('xml.parsers',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/parsers/__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/parsers/expat.py',
   'PYMODULE'),
  ('xml.sax',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/sax/__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/sax/_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/sax/expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/sax/handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/sax/saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xml/sax/xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xmlrpc/__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/xmlrpc/client.py',
   'PYMODULE'),
  ('yuque_books_interceptor',
   '/Users/<USER>/PycharmProjects/PythonProject2/yuque_books_interceptor.py',
   'PYMODULE'),
  ('yuque_downloader',
   '/Users/<USER>/PycharmProjects/PythonProject2/yuque_downloader.py',
   'PYMODULE'),
  ('yuque_webdriver_manager',
   '/Users/<USER>/PycharmProjects/PythonProject2/yuque_webdriver_manager.py',
   'PYMODULE'),
  ('zipfile',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/zipfile/__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/zipfile/_path/__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/zipfile/_path/glob.py',
   'PYMODULE'),
  ('zipimport',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/zipimport.py',
   'PYMODULE'),
  ('zstandard',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/zstandard/__init__.py',
   'PYMODULE'),
  ('zstandard.backend_cffi',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/zstandard/backend_cffi.py',
   'PYMODULE')])
