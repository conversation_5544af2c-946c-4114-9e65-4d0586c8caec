('/Users/<USER>/PycharmProjects/PythonProject2/build/yuque_downloader_terminal/yuque-downloader-v2.1.0.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   '/Users/<USER>/PycharmProjects/PythonProject2/build/yuque_downloader_terminal/PYZ-00.pyz',
   'PYZ'),
  ('lib-dynload/_struct.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_struct.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/zlib.cpython-313-darwin.so',
   'EXTENSION'),
  ('struct',
   '/Users/<USER>/PycharmProjects/PythonProject2/build/yuque_downloader_terminal/localpycs/struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   '/Users/<USER>/PycharmProjects/PythonProject2/build/yuque_downloader_terminal/localpycs/pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   '/Users/<USER>/PycharmProjects/PythonProject2/build/yuque_downloader_terminal/localpycs/pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   '/Users/<USER>/PycharmProjects/PythonProject2/build/yuque_downloader_terminal/localpycs/pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/PyInstaller/loader/pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/_pyinstaller_hooks_contrib/rthooks/pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('main', '/Users/<USER>/PycharmProjects/PythonProject2/main.py', 'PYSOURCE'),
  ('drivers/chromedriver',
   '/Users/<USER>/PycharmProjects/PythonProject2/chromedriver',
   'BINARY'),
  ('selenium/webdriver/common/macos/selenium-manager',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/macos/selenium-manager',
   'BINARY'),
  ('Python.framework/Versions/3.13/Python',
   '/Library/Frameworks/Python.framework/Versions/3.13/Python',
   'BINARY'),
  ('lib-dynload/grp.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/grp.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/math.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/select.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_posixsubprocess.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/fcntl.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/fcntl.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/binascii.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_scproxy.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_scproxy.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/termios.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/resource.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_lzma.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_bz2.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_datetime.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_ssl.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/unicodedata.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/array.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_socket.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_hashlib.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_sha3.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_blake2.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha2.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_sha2.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_md5.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_sha1.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_bisect.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_statistics.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_contextvars.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_decimal.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_random.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_ctypes.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/syslog.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/syslog.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_csv.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_queue.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/mmap.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixshmem.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_posixshmem.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_pickle.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_multiprocessing.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/pyexpat.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_asyncio.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_curses.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_curses.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/readline.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/readline.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_opcode.cpython-313-darwin.so',
   'EXTENSION'),
  ('_cffi_backend.cpython-313-darwin.so',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/_cffi_backend.cpython-313-darwin.so',
   'EXTENSION'),
  ('cryptography/hazmat/bindings/_rust.abi3.so',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography/hazmat/bindings/_rust.abi3.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_json.cpython-313-darwin.so',
   'EXTENSION'),
  ('charset_normalizer/md__mypyc.cpython-313-darwin.so',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/charset_normalizer/md__mypyc.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_multibytecodec.cpython-313-darwin.so',
   'EXTENSION'),
  ('charset_normalizer/md.cpython-313-darwin.so',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/charset_normalizer/md.cpython-313-darwin.so',
   'EXTENSION'),
  ('_brotli.cpython-313-darwin.so',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/_brotli.cpython-313-darwin.so',
   'EXTENSION'),
  ('zstandard/_cffi.cpython-313-darwin.so',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/zstandard/_cffi.cpython-313-darwin.so',
   'EXTENSION'),
  ('zstandard/backend_c.cpython-313-darwin.so',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/zstandard/backend_c.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_uuid.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_uuid.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_heapq.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_codecs_jp.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_codecs_kr.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_codecs_iso2022.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_codecs_cn.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_codecs_tw.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-313-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_codecs_hk.cpython-313-darwin.so',
   'EXTENSION'),
  ('libcrypto.3.dylib',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/libcrypto.3.dylib',
   'BINARY'),
  ('libssl.3.dylib',
   '/Library/Frameworks/Python.framework/Versions/3.13/lib/libssl.3.dylib',
   'BINARY'),
  ('EXCEL_DOWNLOAD_SUMMARY.md',
   '/Users/<USER>/PycharmProjects/PythonProject2/EXCEL_DOWNLOAD_SUMMARY.md',
   'DATA'),
  ('FILENAME_EXTENSION_FIX.md',
   '/Users/<USER>/PycharmProjects/PythonProject2/FILENAME_EXTENSION_FIX.md',
   'DATA'),
  ('INSTALL.md',
   '/Users/<USER>/PycharmProjects/PythonProject2/INSTALL.md',
   'DATA'),
  ('LICENSE', '/Users/<USER>/PycharmProjects/PythonProject2/LICENSE', 'DATA'),
  ('README.md',
   '/Users/<USER>/PycharmProjects/PythonProject2/README.md',
   'DATA'),
  ('__init__.py',
   '/Users/<USER>/PycharmProjects/PythonProject2/__init__.py',
   'DATA'),
  ('api_client.py',
   '/Users/<USER>/PycharmProjects/PythonProject2/api_client.py',
   'DATA'),
  ('api_result.py',
   '/Users/<USER>/PycharmProjects/PythonProject2/api_result.py',
   'DATA'),
  ('build_distribution.py',
   '/Users/<USER>/PycharmProjects/PythonProject2/build_distribution.py',
   'DATA'),
  ('config.py',
   '/Users/<USER>/PycharmProjects/PythonProject2/config.py',
   'DATA'),
  ('enhanced_main.py',
   '/Users/<USER>/PycharmProjects/PythonProject2/enhanced_main.py',
   'DATA'),
  ('exceptions.py',
   '/Users/<USER>/PycharmProjects/PythonProject2/exceptions.py',
   'DATA'),
  ('file_manager.py',
   '/Users/<USER>/PycharmProjects/PythonProject2/file_manager.py',
   'DATA'),
  ('main.py', '/Users/<USER>/PycharmProjects/PythonProject2/main.py', 'DATA'),
  ('path_manager.py',
   '/Users/<USER>/PycharmProjects/PythonProject2/path_manager.py',
   'DATA'),
  ('requirements.txt',
   '/Users/<USER>/PycharmProjects/PythonProject2/requirements.txt',
   'DATA'),
  ('run.py', '/Users/<USER>/PycharmProjects/PythonProject2/run.py', 'DATA'),
  ('seleniumwire/ca.crt',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/ca.crt',
   'DATA'),
  ('seleniumwire/ca.key',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/seleniumwire/ca.key',
   'DATA'),
  ('setup.py',
   '/Users/<USER>/PycharmProjects/PythonProject2/setup.py',
   'DATA'),
  ('sync_auth.py',
   '/Users/<USER>/PycharmProjects/PythonProject2/sync_auth.py',
   'DATA'),
  ('test_csrf_fix.py',
   '/Users/<USER>/PycharmProjects/PythonProject2/test_csrf_fix.py',
   'DATA'),
  ('user_interface.py',
   '/Users/<USER>/PycharmProjects/PythonProject2/user_interface.py',
   'DATA'),
  ('utils.py',
   '/Users/<USER>/PycharmProjects/PythonProject2/utils.py',
   'DATA'),
  ('yuque_books_interceptor.py',
   '/Users/<USER>/PycharmProjects/PythonProject2/yuque_books_interceptor.py',
   'DATA'),
  ('yuque_downloader.py',
   '/Users/<USER>/PycharmProjects/PythonProject2/yuque_downloader.py',
   'DATA'),
  ('yuque_webdriver_manager.py',
   '/Users/<USER>/PycharmProjects/PythonProject2/yuque_webdriver_manager.py',
   'DATA'),
  ('cryptography-45.0.4.dist-info/licenses/LICENSE.BSD',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography-45.0.4.dist-info/licenses/LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.4.dist-info/licenses/LICENSE',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography-45.0.4.dist-info/licenses/LICENSE',
   'DATA'),
  ('cryptography-45.0.4.dist-info/licenses/LICENSE.APACHE',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography-45.0.4.dist-info/licenses/LICENSE.APACHE',
   'DATA'),
  ('cryptography-45.0.4.dist-info/RECORD',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography-45.0.4.dist-info/RECORD',
   'DATA'),
  ('cryptography-45.0.4.dist-info/INSTALLER',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography-45.0.4.dist-info/INSTALLER',
   'DATA'),
  ('cryptography-45.0.4.dist-info/WHEEL',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography-45.0.4.dist-info/WHEEL',
   'DATA'),
  ('cryptography-45.0.4.dist-info/METADATA',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/cryptography-45.0.4.dist-info/METADATA',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL',
   'DATA'),
  ('setuptools/_vendor/jaraco/text/Lorem ipsum.txt',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/Lorem '
   'ipsum.txt',
   'DATA'),
  ('certifi/cacert.pem',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/certifi/cacert.pem',
   'DATA'),
  ('certifi/py.typed',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/certifi/py.typed',
   'DATA'),
  ('selenium/webdriver/common/devtools/v134/py.typed',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/devtools/v134/py.typed',
   'DATA'),
  ('selenium/webdriver/remote/findElements.js',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/remote/findElements.js',
   'DATA'),
  ('selenium/webdriver/common/windows/selenium-manager.exe',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/windows/selenium-manager.exe',
   'DATA'),
  ('selenium/webdriver/common/devtools/v136/py.typed',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/devtools/v136/py.typed',
   'DATA'),
  ('selenium/webdriver/remote/isDisplayed.js',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/remote/isDisplayed.js',
   'DATA'),
  ('selenium/py.typed',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/py.typed',
   'DATA'),
  ('selenium/webdriver/firefox/webdriver_prefs.json',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/firefox/webdriver_prefs.json',
   'DATA'),
  ('selenium/webdriver/remote/getAttribute.js',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/remote/getAttribute.js',
   'DATA'),
  ('selenium/webdriver/common/mutation-listener.js',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/mutation-listener.js',
   'DATA'),
  ('selenium/webdriver/common/linux/selenium-manager',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/linux/selenium-manager',
   'DATA'),
  ('selenium/webdriver/common/devtools/v135/py.typed',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/selenium/webdriver/common/devtools/v135/py.typed',
   'DATA'),
  ('Python', 'Python.framework/Versions/3.13/Python', 'SYMLINK'),
  ('attrs-25.3.0.dist-info/WHEEL',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/attrs-25.3.0.dist-info/WHEEL',
   'DATA'),
  ('attrs-25.3.0.dist-info/INSTALLER',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/attrs-25.3.0.dist-info/INSTALLER',
   'DATA'),
  ('attrs-25.3.0.dist-info/METADATA',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/attrs-25.3.0.dist-info/METADATA',
   'DATA'),
  ('attrs-25.3.0.dist-info/RECORD',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/attrs-25.3.0.dist-info/RECORD',
   'DATA'),
  ('trio-0.30.0.dist-info/INSTALLER',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio-0.30.0.dist-info/INSTALLER',
   'DATA'),
  ('trio-0.30.0.dist-info/WHEEL',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio-0.30.0.dist-info/WHEEL',
   'DATA'),
  ('trio-0.30.0.dist-info/licenses/LICENSE',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio-0.30.0.dist-info/licenses/LICENSE',
   'DATA'),
  ('trio-0.30.0.dist-info/METADATA',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio-0.30.0.dist-info/METADATA',
   'DATA'),
  ('trio-0.30.0.dist-info/licenses/LICENSE.MIT',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio-0.30.0.dist-info/licenses/LICENSE.MIT',
   'DATA'),
  ('attrs-25.3.0.dist-info/licenses/LICENSE',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/attrs-25.3.0.dist-info/licenses/LICENSE',
   'DATA'),
  ('trio-0.30.0.dist-info/top_level.txt',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio-0.30.0.dist-info/top_level.txt',
   'DATA'),
  ('trio-0.30.0.dist-info/entry_points.txt',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio-0.30.0.dist-info/entry_points.txt',
   'DATA'),
  ('trio-0.30.0.dist-info/RECORD',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio-0.30.0.dist-info/RECORD',
   'DATA'),
  ('trio-0.30.0.dist-info/licenses/LICENSE.APACHE2',
   '/Users/<USER>/PycharmProjects/PythonProject2/.venv/lib/python3.13/site-packages/trio-0.30.0.dist-info/licenses/LICENSE.APACHE2',
   'DATA'),
  ('base_library.zip',
   '/Users/<USER>/PycharmProjects/PythonProject2/build/yuque_downloader_terminal/base_library.zip',
   'DATA'),
  ('Python.framework/Python', 'Versions/Current/Python', 'SYMLINK'),
  ('Python.framework/Resources', 'Versions/Current/Resources', 'SYMLINK'),
  ('Python.framework/Versions/3.13/Resources/Info.plist',
   '/Library/Frameworks/Python.framework/Versions/3.13/Resources/Info.plist',
   'DATA'),
  ('Python.framework/Versions/Current', '3.13', 'SYMLINK')],
 'Python',
 False,
 False,
 False,
 [],
 'arm64',
 None,
 None)
