#!/usr/bin/env python3
"""
简化的OSS下载测试脚本
"""

import time
import urllib.parse
import requests
from pathlib import Path

def analyze_oss_url(url):
    """分析OSS URL"""
    print(f"分析URL: {url}")
    
    try:
        parsed = urllib.parse.urlparse(url)
        params = urllib.parse.parse_qs(parsed.query)
        
        print(f"域名: {parsed.netloc}")
        print(f"路径: {parsed.path}")
        
        # 检查OSS参数
        oss_params = ['OSSAccessKeyId', 'Expires', 'Signature']
        for param in oss_params:
            if param in params:
                print(f"{param}: {params[param][0]}")
        
        # 检查过期时间
        if 'Expires' in params:
            expires_timestamp = int(params['Expires'][0])
            current_timestamp = int(time.time())
            
            print(f"\n时间分析:")
            print(f"当前时间戳: {current_timestamp}")
            print(f"过期时间戳: {expires_timestamp}")
            print(f"时间差: {expires_timestamp - current_timestamp} 秒")
            
            # 转换为可读时间
            import datetime
            current_time = datetime.datetime.fromtimestamp(current_timestamp)
            expire_time = datetime.datetime.fromtimestamp(expires_timestamp)
            
            print(f"当前时间: {current_time}")
            print(f"过期时间: {expire_time}")
            
            if current_timestamp >= expires_timestamp:
                print("❌ URL已过期")
                return False
            else:
                remaining = expires_timestamp - current_timestamp
                print(f"✅ URL仍然有效，剩余时间: {remaining}秒 ({remaining/3600:.1f}小时)")
                return True
        
        return True
        
    except Exception as e:
        print(f"❌ 分析URL失败: {e}")
        return False

def test_download_strategies(url):
    """测试不同的下载策略"""
    print(f"\n=== 测试下载策略 ===")
    
    test_dir = Path("./test_downloads")
    test_dir.mkdir(exist_ok=True)
    test_file = test_dir / "test_document.docx"
    
    strategies = [
        ("直接下载", {}),
        ("最小头部", {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }),
        ("标准浏览器头部", {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br'
        }),
        ("带Referer", {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Referer': 'https://www.yuque.com'
        })
    ]
    
    for strategy_name, headers in strategies:
        print(f"\n尝试策略: {strategy_name}")
        try:
            session = requests.Session()
            response = session.get(url, headers=headers, stream=True, timeout=30)
            
            print(f"状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                # 尝试下载一小部分内容
                content = b""
                for chunk in response.iter_content(chunk_size=1024):
                    content += chunk
                    if len(content) > 10240:  # 只下载前10KB测试
                        break
                
                print(f"✅ {strategy_name} 成功，下载了 {len(content)} 字节")
                
                # 保存测试文件
                with open(test_file, 'wb') as f:
                    f.write(content)
                print(f"测试文件保存到: {test_file}")
                break
            else:
                print(f"❌ {strategy_name} 失败: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ {strategy_name} 异常: {e}")
    
    # 清理测试文件
    if test_file.exists():
        test_file.unlink()

def analyze_problematic_headers():
    """分析可能有问题的请求头"""
    print("\n=== 分析可能有问题的请求头 ===")
    
    # 你提供的原始请求头
    original_headers = {
        'Accept': 'application/json',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Connection': 'keep-alive',
        'Content-Length': '25',
        'Content-Type': 'application/json',
        'Cookie': 'lang=zh-cn; receive-cookie-deprecation=1; _yuque_session=...',
        'DNT': '1',
        'Host': 'www.yuque.com',
        'Origin': 'https://www.yuque.com',
        'Referer': 'https://www.yuque.com/wangchangkai/bzu4os/uoz6nelmfhbczqbr',
        'X-Requested-With': 'XMLHttpRequest',
        'x-csrf-token': 'rChkDAKNbvhBDb9Ppu9rDDEN',
        'x-login': 'wangchangkai'
    }
    
    # 可能干扰OSS签名的头部
    problematic_for_oss = [
        'Cookie', 'X-Requested-With', 'x-csrf-token', 'Authorization', 
        'Origin', 'Content-Type', 'Content-Length', 'x-login'
    ]
    
    print("原始请求头（用于语雀API）:")
    for key, value in original_headers.items():
        marker = "⚠️" if key in problematic_for_oss else "✅"
        print(f"  {marker} {key}: {value[:50]}...")
    
    print(f"\n可能干扰OSS下载的头部:")
    for header in problematic_for_oss:
        if header in original_headers:
            print(f"  ❌ {header}: 应该在OSS下载时移除")
    
    print(f"\nOSS下载推荐的最小头部:")
    recommended_headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': '*/*'
    }
    for key, value in recommended_headers.items():
        print(f"  ✅ {key}: {value}")

def main():
    """主函数"""
    print("🔧 语雀OSS下载问题诊断工具")
    print("=" * 50)
    
    # 你提供的失败URL
    test_url = "https://lark-temp.oss-cn-hangzhou.aliyuncs.com/__temp/0/docx/2a896a05-7f0a-43cc-8ec3-abaa9df5c13a.docx?OSSAccessKeyId=LTAI4GKnqTWmz2X8mzA1Sjbv&Expires=1751626205&Signature=XDC%2FHl8g1JR0nga05%2F%2Bgi307gbU%3D"
    
    try:
        # 分析URL
        is_valid = analyze_oss_url(test_url)
        
        # 分析请求头
        analyze_problematic_headers()
        
        # 如果URL有效，测试下载
        if is_valid:
            test_download_strategies(test_url)
        else:
            print("\n⚠️ URL已过期，跳过下载测试")
            print("建议：需要重新导出文档获取新的下载URL")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
