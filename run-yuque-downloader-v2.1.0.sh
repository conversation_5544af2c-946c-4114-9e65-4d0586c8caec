#!/bin/bash

# 语雀下载工具 v2.1.0 启动脚本
# 支持Excel文档自动下载功能

echo "🚀 语雀下载工具 v2.1.0"
echo "✨ 新功能: Excel文档自动下载支持"
echo "======================================="
echo

# 检查可执行文件是否存在
EXECUTABLE="./dist/yuque-downloader-v2.1.0"

if [ ! -f "$EXECUTABLE" ]; then
    echo "❌ 错误: 找不到可执行文件 $EXECUTABLE"
    echo "请确保在项目根目录运行此脚本"
    exit 1
fi

# 检查可执行权限
if [ ! -x "$EXECUTABLE" ]; then
    echo "🔧 设置可执行权限..."
    chmod +x "$EXECUTABLE"
fi

echo "🎯 启动语雀下载工具..."
echo "💡 提示: 现在支持Excel文档(.xlsx)自动下载"
echo "======================================="
echo

# 启动程序
"$EXECUTABLE"

echo
echo "======================================="
echo "📋 程序已退出"
echo "💾 会话信息已保存，下次启动时自动恢复"
echo "📁 下载的文件保存在 dist/downloads/ 目录"
echo "📊 Excel文档现在正确显示为 .xlsx 扩展名"
