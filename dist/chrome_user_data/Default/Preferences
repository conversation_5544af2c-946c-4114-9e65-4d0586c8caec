{"NewTabPage": {"PrevNavigationTime": "*****************"}, "accessibility": {"captions": {"headless_caption_enabled": false, "live_caption_language": "cmn-Hans-CN"}}, "account_tracker_service_last_update": "*****************", "ack_existing_ntp_extensions": true, "alternate_error_pages": {"backup": false}, "autocomplete": {"retention_policy_last_version": 137}, "autofill": {"last_version_deduped": 137, "upload_events_last_reset_timestamp": "*****************"}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"check_default_browser": false, "clear_data": {"time_period_basic": 4}, "window_placement": {"bottom": 1120, "left": 1920, "maximized": false, "right": 3432, "top": 176, "work_area_bottom": 1120, "work_area_left": 1920, "work_area_right": 3432, "work_area_top": 176}}, "chrome": {"prefetch_proxy": {"origin_decider": {"retry_after": {}}}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 17230, "default_search_provider": {"guid": ""}, "devtools": {"last_open_timestamp": "**************", "preferences": {"closeable-tabs": "{\"security\":true,\"freestyler\":true,\"chrome-recorder\":true}", "currentDockState": "\"right\"", "elements.styles.sidebar.width": "{\"vertical\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "inspector-view.split-view-state": "{\"vertical\":{\"size\":0}}", "inspector.drawer-split-view-state": "{\"horizontal\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "inspectorVersion": "38", "releaseNoteVersionSeen": "78", "styles-pane-sidebar-tab-order": "{\"styles\":10,\"computed\":20}"}, "synced_preferences_sync_disabled": {"adorner-settings": "[{\"adorner\":\"grid\",\"isEnabled\":true},{\"adorner\":\"subgrid\",\"isEnabled\":true},{\"adorner\":\"flex\",\"isEnabled\":true},{\"adorner\":\"ad\",\"isEnabled\":true},{\"adorner\":\"scroll-snap\",\"isEnabled\":true},{\"adorner\":\"container\",\"isEnabled\":true},{\"adorner\":\"slot\",\"isEnabled\":true},{\"adorner\":\"top-layer\",\"isEnabled\":true},{\"adorner\":\"reveal\",\"isEnabled\":true},{\"adorner\":\"media\",\"isEnabled\":false},{\"adorner\":\"scroll\",\"isEnabled\":true}]", "syncedInspectorVersion": "38"}}, "distribution": {"import_bookmarks": false, "import_history": false, "import_search_engine": false, "make_chrome_default_for_user": false, "skip_first_run_ui": true}, "dns_prefetching": {"enabled": false}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "13c4e266-bd65-4855-a781-e87858ec43b8", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}}, "gaia_cookie": {"changed_time": **********.42322, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "com.chrome.macosx", "push_messaging_unsubscribed_entries_list": []}, "google": {"services": {"signin_scoped_device_id": "3bd1ab59-ec3b-4b0c-8611-3de969400df7"}}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "session_last_active_time": "*****************", "session_start_time": "*****************"}, "intl": {"selected_languages": "zh-CN,zh"}, "invalidation": {"per_sender_topics_to_handler": {"*************": {}}}, "media": {"device_id_salt": "1E7D375C3F0940109460A788419F3F6E", "engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "LC7GFVGdCjFOlSEZAipYqCmdpcIGnJVNllW8J+woWHOvB+ktsn/eZbbKhx7rg8wcCCzVzdArzBjzxh/VapFVlg=="}, "net": {"network_prediction_options": 0}, "ntp": {"num_personal_suggestions": 2}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "predictionmodelfetcher": {"last_fetch_attempt": "*****************", "last_fetch_success": "*****************"}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PRICE_TRACKING": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true, "relaunch_chrome_bubble_dismissed_counter": 0}, "prefetch": {"search_prefetch": {"cache": {}}}, "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true, "topics_data_accessible_since": "*****************"}, "profile": {"avatar_index": 26, "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 8, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"https://[*.]yuque.com,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"https://fjzx.yuque.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 5}}, "https://www.yuque.com:443,*": {"expiration": "13404109437838311", "last_modified": "13396333437838315", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 2}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"chrome://newtab/,*": {"last_modified": "13396333401607333", "setting": {"lastEngagementTime": 1.339633340160732e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 7.5, "rawScore": 7.5}}, "https://fjzx.yuque.com:443,*": {"last_modified": "13396333405810536", "setting": {"lastEngagementTime": 1.3396333405810526e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 14.1, "rawScore": 14.1}}, "https://www.yuque.com:443,*": {"last_modified": "13396326384719862", "setting": {"lastEngagementTime": 1.3396326384719856e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 2.1, "rawScore": 2.1}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pattern_pairs": {"https://*,*": {"media-stream": {"audio": "<PERSON><PERSON><PERSON>", "video": "<PERSON><PERSON><PERSON>"}}}, "pref_version": 1}, "creation_time": "13395407402683395", "default_content_setting_values": {"geolocation": 1, "notifications": 2}, "default_content_settings": {"geolocation": 1, "mouselock": 1, "notifications": 1, "popups": 1, "ppapi-broker": 1}, "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "exit_type": "Normal", "family_member_role": "not_in_family", "last_engagement_time": "13396333405810526", "last_time_obsolete_http_credentials_removed": 1750933890.807932, "last_time_password_store_metrics_reported": 1751851908.936328, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_default_content_settings": {"images": 1}, "managed_user_id": "", "name": "您的 Chrome", "password_hash_data_list": [], "password_manager_enabled": false, "safety_hub_menu_notifications": {"extensions": {"isCurrentlyActive": false, "result": {"timestamp": "13396326254780056", "triggeringExtensions": []}}, "notification-permissions": {"isCurrentlyActive": false, "result": {"notificationPermissions": [], "timestamp": "13396326249300447"}}, "passwords": {"isCurrentlyActive": false, "result": {"passwordCheckOrigins": [], "timestamp": "13396326249303122"}}, "safe-browsing": {"isCurrentlyActive": false, "onlyShowAfterTime": "13396412649275961", "result": {"safeBrowsingStatus": 4, "timestamp": "13396326254780041"}}, "unused-site-permissions": {"isCurrentlyActive": false, "result": {"permissions": [], "timestamp": "13396326242430949"}}}, "were_old_google_logins_removed": true}, "safebrowsing": {"enabled": false, "event_timestamps": {}, "metrics_last_log_time": "13396325478", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "search": {"suggest_enabled": false}, "segmentation_platform": {"client_result_prefs": "CmAKEXJlc3VtZV9oZWF2eV91c2VyEksKQA0AAAAAEOTF29vZ9eUXGi4KJgokDQAAAD8SFlJlc3VtZUhlYXZ5VXNlclNlZ21lbnQaBU90aGVyEgQQDhgEIAIQ9sXb29n15RcKcwoVcGFzc3dvcmRfbWFuYWdlcl91c2VyEloKTw0AAAAAEO/I29vZ9eUXGj0KNQozDQAAAD8SE1Bhc3N3b3JkTWFuYWdlclVzZXIaF05vdF9QYXNzd29yZE1hbmFnZXJVc2VyEgQQBxgEIAEQjsnb29n15RcK5QIKEWNyb3NzX2RldmljZV91c2VyEs8CCsMCDQAAgD8Q2MTb29n15RcasAIKpwIapAIKGQ0AAIA/EhJOb0Nyb3NzRGV2aWNlVXNhZ2UKGA0AAABAEhFDcm9zc0RldmljZU1vYmlsZQoZDQAAQEASEkNyb3NzRGV2aWNlRGVza3RvcAoYDQAAgEASEUNyb3NzRGV2aWNlVGFibGV0CiINAACgQBIbQ3Jvc3NEZXZpY2VNb2JpbGVBbmREZXNrdG9wCiENAADAQBIaQ3Jvc3NEZXZpY2VNb2JpbGVBbmRUYWJsZXQKIg0AAOBAEhtDcm9zc0RldmljZURlc2t0b3BBbmRUYWJsZXQKIA0AAABBEhlDcm9zc0RldmljZUFsbERldmljZVR5cGVzChcNAAAQQRIQQ3Jvc3NEZXZpY2VPdGhlchISTm9Dcm9zc0RldmljZVVzYWdlEgQQBxgEIAIQvsXb29n15RcKUgoNc2hvcHBpbmdfdXNlchJBCjYNAAAAABC98qausfzlFxokChwKGg0AAAA/EgxTaG9wcGluZ1VzZXIaBU90aGVyEgQQAhgEIAMQ7vKmrrH85RcKZAoLc2VhcmNoX3VzZXISVQpKDQAAAAAQxc7b29n15RcaOAowGi4KCg0AAIA/EgNMb3cKDQ0AAKBAEgZNZWRpdW0KCw0AALBBEgRIaWdoEgROb25lEgQQBxgEIAIQ5s7b29n15Rc=", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13396233599000000", "uma_in_sql_start_time": "13395407403216228"}, "sessions": {"event_log": [{"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13396100823224839", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396100927992651", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13396100959777680", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396100980439406", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13396101012572358", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396325478728687", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13396325528485244", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396326155337813", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13396326174253814", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396326235163122", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13396326265147220", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396326283155385", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13396326487032965", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396327049214522", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13396327070080464", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396333156658108", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "*****************", "type": 2, "window_count": 1}, {"crashed": false, "time": "*****************", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "*****************", "type": 2, "window_count": 1}], "session_data_status": 5}, "settings": {"force_google_safesearch": false}, "signin": {"allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "sync": {"passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate": {"enabled": false}, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "137"}, "webauthn": {"touchid": {"metadata_secret": "U2VLtYjVMZHETT11HWaGIn6Owa+lKkfwztFdl5JmRYo="}}, "zerosuggest": {"cachedresults": "", "cachedresults_with_url": {}}}