@echo off
chcp 65001 >nul
title 语雀文档下载器

echo.
echo ========================================
echo 🚀 语雀文档下载器 Windows版
echo ========================================
echo ✨ 增强功能:
echo   📋 详细的下载日志分类显示
echo   ✅ 成功下载文档清单
echo   🔄 跳过文档统计
echo   ❌ 失败文档详细分析
echo   💡 智能错误建议
echo ========================================
echo.

REM 检查可执行文件是否存在
if not exist "yuque-downloader.exe" (
    echo ❌ 错误: 找不到 yuque-downloader.exe
    echo 请确保在正确的目录运行此脚本
    pause
    exit /b 1
)

echo 🎯 启动语雀文档下载器...
echo 💡 提示: 程序将自动打开Chrome浏览器
echo 💡 提示: 请在浏览器中登录您的语雀账号
echo 💡 提示: 下载完成后将生成详细的日志文件
echo.
echo ========================================
echo.

REM 启动程序
yuque-downloader.exe

echo.
echo ========================================
echo 📋 程序已退出
echo 💾 会话信息已保存，下次启动时自动恢复
echo 📁 下载的文件保存在 downloads\ 目录
echo 📄 详细日志文件保存在程序目录
echo ========================================
echo.
pause
