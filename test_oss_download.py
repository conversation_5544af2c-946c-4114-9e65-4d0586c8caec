#!/usr/bin/env python3
"""
测试OSS下载修复的脚本
"""

import sys
import os
import time
import urllib.parse
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api_client import YuqueAP<PERSON>lient
from yuque_webdriver_manager import YuqueWebDriverManager
from config import DOWNLOAD_CONFIG

def test_oss_url_validity():
    """测试OSS URL有效性检查"""
    print("=== 测试OSS URL有效性检查 ===")
    
    # 创建一个临时的API客户端实例
    webdriver_manager = YuqueWebDriverManager()
    api_client = YuqueAPIClient(webdriver_manager)
    
    # 测试URL（你提供的失败URL）
    test_url = "https://lark-temp.oss-cn-hangzhou.aliyuncs.com/__temp/0/docx/2a896a05-7f0a-43cc-8ec3-abaa9df5c13a.docx?OSSAccessKeyId=LTAI4GKnqTWmz2X8mzA1Sjbv&Expires=1751626205&Signature=XDC%2FHl8g1JR0nga05%2F%2Bgi307gbU%3D"
    
    # 分析URL类型
    url_type = api_client._analyze_url_type(test_url)
    print(f"URL类型: {url_type}")
    
    # 检查URL有效性
    is_valid = api_client._check_oss_url_validity(test_url)
    print(f"URL有效性: {is_valid}")
    
    # 解析URL参数
    parsed = urllib.parse.urlparse(test_url)
    params = urllib.parse.parse_qs(parsed.query)
    
    if 'Expires' in params:
        expires_timestamp = int(params['Expires'][0])
        current_timestamp = int(time.time())
        
        print(f"当前时间戳: {current_timestamp}")
        print(f"过期时间戳: {expires_timestamp}")
        print(f"时间差: {expires_timestamp - current_timestamp} 秒")
        
        # 转换为可读时间
        import datetime
        current_time = datetime.datetime.fromtimestamp(current_timestamp)
        expire_time = datetime.datetime.fromtimestamp(expires_timestamp)
        
        print(f"当前时间: {current_time}")
        print(f"过期时间: {expire_time}")
        
        if current_timestamp >= expires_timestamp:
            print("❌ URL已过期")
        else:
            print("✅ URL仍然有效")
    
    api_client.close()
    webdriver_manager.close()

def test_download_strategies():
    """测试不同的下载策略"""
    print("\n=== 测试下载策略 ===")
    
    # 创建测试目录
    test_dir = Path("./test_downloads")
    test_dir.mkdir(exist_ok=True)
    
    webdriver_manager = YuqueWebDriverManager()
    api_client = YuqueAPIClient(webdriver_manager)
    
    # 测试URL
    test_url = "https://lark-temp.oss-cn-hangzhou.aliyuncs.com/__temp/0/docx/2a896a05-7f0a-43cc-8ec3-abaa9df5c13a.docx?OSSAccessKeyId=LTAI4GKnqTWmz2X8mzA1Sjbv&Expires=1751626205&Signature=XDC%2FHl8g1JR0nga05%2F%2Bgi307gbU%3D"
    test_file = test_dir / "test_document.docx"
    
    print(f"测试下载URL: {test_url}")
    print(f"保存路径: {test_file}")
    
    # 测试新的下载方法
    try:
        success = api_client.download_file(test_url, str(test_file))
        if success:
            print("✅ 下载成功")
            if test_file.exists():
                print(f"文件大小: {test_file.stat().st_size} 字节")
            else:
                print("❌ 文件不存在")
        else:
            print("❌ 下载失败")
    except Exception as e:
        print(f"❌ 下载异常: {e}")
    
    # 清理
    if test_file.exists():
        test_file.unlink()
    
    api_client.close()
    webdriver_manager.close()

def analyze_request_headers():
    """分析请求头信息"""
    print("\n=== 分析请求头信息 ===")
    
    # 你提供的请求头
    headers = {
        'Accept': 'application/json',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Connection': 'keep-alive',
        'Content-Length': '25',
        'Content-Type': 'application/json',
        'Cookie': 'lang=zh-cn; receive-cookie-deprecation=1; _yuque_session=0zSskrHCtwRrU-e2OJ1JVs2YBCiHLfHDCYh7iPYyjJIJLaWr_LqG-BhUTagSMGGQ4s742vfXCys6JgiWSvLgUg==; _uab_collina=174385784404471583622505; aliyungf_tc=db1ed70d2e30b1c96bdd54d5dde8376db8fcdb81d365f7cbd0a284565a462f7e; yuque_ctoken=rChkDAKNbvhBDb9Ppu9rDDEN; current_theme=default; acw_tc=ac11000117516252866586089ee6d97a49547010ee0c48a0f4abae13a8903e',
        'DNT': '1',
        'Host': 'www.yuque.com',
        'Origin': 'https://www.yuque.com',
        'Referer': 'https://www.yuque.com/wangchangkai/bzu4os/uoz6nelmfhbczqbr',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'X-Requested-With': 'XMLHttpRequest',
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'x-csrf-token': 'rChkDAKNbvhBDb9Ppu9rDDEN',
        'x-login': 'wangchangkai'
    }
    
    print("导出请求的头部信息:")
    for key, value in headers.items():
        print(f"  {key}: {value}")
    
    print("\n可能影响OSS下载的头部:")
    problematic_headers = ['Cookie', 'X-Requested-With', 'x-csrf-token', 'Authorization', 'Referer']
    for header in problematic_headers:
        if header in headers or header.lower() in [h.lower() for h in headers.keys()]:
            print(f"  ⚠️ {header}: 可能干扰OSS签名验证")

def main():
    """主函数"""
    print("🔧 语雀OSS下载问题诊断工具")
    print("=" * 50)
    
    try:
        # 测试1: URL有效性检查
        test_oss_url_validity()
        
        # 测试2: 分析请求头
        analyze_request_headers()
        
        # 测试3: 下载策略测试
        test_download_strategies()
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
