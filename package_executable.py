#!/usr/bin/env python3
"""
打包语雀下载器可执行文件
创建包含可执行文件和必要文档的分发包
"""

import os
import shutil
import zipfile
import datetime
from pathlib import Path

def get_current_time():
    """获取当前时间戳"""
    return datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

def create_executable_package():
    """创建可执行文件分发包"""
    print("🚀 开始打包语雀下载器可执行文件...")
    
    # 检查可执行文件是否存在
    executable_path = "dist/yuque-downloader"
    if not os.path.exists(executable_path):
        print(f"❌ 错误: 找不到可执行文件 {executable_path}")
        return False
    
    # 创建临时打包目录
    timestamp = get_current_time()
    package_name = f"yuque-downloader-executable-{timestamp}"
    package_dir = Path(package_name)
    
    if package_dir.exists():
        shutil.rmtree(package_dir)
    
    package_dir.mkdir()
    print(f"📁 创建打包目录: {package_dir}")
    
    # 复制可执行文件
    print("📋 复制文件...")
    
    # 1. 复制主可执行文件
    shutil.copy2(executable_path, package_dir / "yuque-downloader")
    print("   ✅ yuque-downloader (可执行文件)")
    
    # 2. 复制启动脚本（如果存在）
    if os.path.exists("dist/run-yuque-downloader.sh"):
        shutil.copy2("dist/run-yuque-downloader.sh", package_dir / "run-yuque-downloader.sh")
        print("   ✅ run-yuque-downloader.sh")
    
    # 3. 复制必要的文档文件
    doc_files = [
        'README.md',
        'LICENSE', 
        'INSTALL.md',
        'requirements.txt'
    ]
    
    for doc_file in doc_files:
        if os.path.exists(doc_file):
            shutil.copy2(doc_file, package_dir / doc_file)
            print(f"   ✅ {doc_file}")
    
    # 4. 创建目录结构
    directories = ['downloads', 'drivers', 'logs']
    for dir_name in directories:
        dir_path = package_dir / dir_name
        dir_path.mkdir()
        # 创建.gitkeep文件
        (dir_path / '.gitkeep').touch()
        print(f"   ✅ {dir_name}/ (目录)")
    
    # 5. 复制ChromeDriver（如果存在）
    chromedriver_path = "dist/drivers/chromedriver"
    if os.path.exists(chromedriver_path):
        drivers_dir = package_dir / "drivers"
        shutil.copy2(chromedriver_path, drivers_dir / "chromedriver")
        print("   ✅ drivers/chromedriver")
    
    # 6. 创建使用说明
    create_usage_guide(package_dir)
    
    # 7. 创建ZIP包
    zip_name = f"{package_name}.zip"
    create_zip_from_directory(package_dir, zip_name)
    
    # 8. 清理临时目录
    shutil.rmtree(package_dir)
    
    print(f"\n🎉 打包完成!")
    print(f"📦 分发包: {zip_name}")
    print(f"📊 文件大小: {get_file_size(zip_name)}")
    
    return True

def create_usage_guide(package_dir):
    """创建使用说明"""
    guide_content = '''# 🚀 语雀下载器 - 使用说明

## 📋 文件说明

- `yuque-downloader` - 主程序可执行文件
- `run-yuque-downloader.sh` - 启动脚本（macOS/Linux）
- `downloads/` - 下载文件存储目录
- `drivers/` - ChromeDriver存储目录
- `logs/` - 程序日志目录

## 🎯 快速开始

### macOS/Linux用户
```bash
# 方法1: 直接运行
./yuque-downloader

# 方法2: 使用启动脚本
chmod +x run-yuque-downloader.sh
./run-yuque-downloader.sh
```

### Windows用户
双击运行 `yuque-downloader.exe`

## 📋 使用流程

1. **启动程序** - 运行可执行文件
2. **浏览器登录** - 在自动打开的Chrome浏览器中登录语雀账号
3. **选择知识库** - 从拦截到的列表中选择要下载的知识库
4. **选择模式** - 全部下载或精选下载
5. **开始下载** - 确认后程序自动下载到 `downloads/` 目录

## 💡 注意事项

- 首次运行需要安装Chrome浏览器
- 确保网络连接稳定
- 保持语雀登录状态
- 下载的文件保存在 `downloads/` 目录

## 📞 技术支持

- 查看日志: `logs/yuque_downloader.log`
- 问题反馈: GitHub Issues
- 详细文档: README.md

---
💡 提示: 程序会自动管理ChromeDriver，无需手动下载
'''
    
    with open(package_dir / 'USAGE.md', 'w', encoding='utf-8') as f:
        f.write(guide_content)
    print("   ✅ USAGE.md (使用说明)")

def create_zip_from_directory(source_dir, zip_name):
    """从目录创建ZIP文件"""
    print(f"\n📦 创建ZIP包: {zip_name}")
    
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(source_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, source_dir)
                zipf.write(file_path, arc_name)
                print(f"   📄 {arc_name}")

def get_file_size(file_path):
    """获取文件大小（人类可读格式）"""
    size = os.path.getsize(file_path)
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size < 1024.0:
            return f"{size:.1f} {unit}"
        size /= 1024.0
    return f"{size:.1f} TB"

def main():
    """主函数"""
    print("=" * 50)
    print("🚀 语雀下载器可执行文件打包工具")
    print("=" * 50)
    
    try:
        success = create_executable_package()
        if success:
            print("\n✅ 打包成功完成!")
            print("📦 可执行文件分发包已创建")
            print("🎯 用户可以直接下载并运行")
        else:
            print("\n❌ 打包失败")
            return 1
            
    except Exception as e:
        print(f"\n❌ 打包过程中出现错误: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
