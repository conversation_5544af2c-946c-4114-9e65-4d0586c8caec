#!/usr/bin/env python3
"""
测试旧版本修复的脚本
"""

import sys
import os
from pathlib import Path

# 添加旧版本目录到Python路径
old_version_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'yuque-downloader-v2.1.0-20250704_173606')
sys.path.insert(0, old_version_path)

def test_old_version_download():
    """测试旧版本的下载功能"""
    try:
        # 创建一个最小的测试环境
        class MockWebDriverManager:
            def __init__(self):
                self.driver = None
            def get_headers(self):
                return {}
            def close(self):
                pass
        
        from api_client import YuqueAPIClient
        
        mock_wm = MockWebDriverManager()
        client = YuqueAPIClient(mock_wm)
        
        test_url = "https://lark-temp.oss-cn-hangzhou.aliyuncs.com/__temp/0/docx/2a896a05-7f0a-43cc-8ec3-abaa9df5c13a.docx?OSSAccessKeyId=LTAI4GKnqTWmz2X8mzA1Sjbv&Expires=1751626205&Signature=XDC%2FHl8g1JR0nga05%2F%2Bgi307gbU%3D"
        
        # 检查URL类型
        url_type = client._analyze_url_type(test_url)
        print(f"旧版本URL类型检测: {url_type}")
        
        # 检查是否有新的下载方法
        has_oss_signed_url = hasattr(client, '_download_oss_signed_url')
        has_oss_minimal_headers = hasattr(client, '_download_oss_minimal_headers')
        
        print(f"旧版本是否有 _download_oss_signed_url 方法: {has_oss_signed_url}")
        print(f"旧版本是否有 _download_oss_minimal_headers 方法: {has_oss_minimal_headers}")
        
        # 测试下载文件方法的策略选择逻辑
        test_dir = Path("./test_downloads")
        test_dir.mkdir(exist_ok=True)
        test_file = test_dir / "test_document_old.docx"
        
        print(f"\n开始测试旧版本下载...")
        print(f"测试文件: {test_file}")
        
        if url_type == 'oss_standard' and has_oss_signed_url:
            print("✅ 旧版本检测到OSS标准URL，且有OSS专用策略")
            
            # 测试OSS签名URL下载方法
            try:
                result = client._download_oss_signed_url(test_url, str(test_file))
                print(f"旧版本OSS签名URL下载测试结果: {result}")
                
                if result and test_file.exists():
                    file_size = test_file.stat().st_size
                    print(f"✅ 旧版本下载成功，文件大小: {file_size} 字节")
                else:
                    print("❌ 旧版本下载失败")
                    
            except Exception as e:
                print(f"旧版本OSS签名URL下载测试失败: {e}")
        else:
            print("❌ 旧版本URL类型不是oss_standard或缺少OSS方法")
        
        # 清理测试文件
        if test_file.exists():
            test_file.unlink()
        
        client.close()
        
    except Exception as e:
        print(f"旧版本测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🔧 旧版本修复测试工具")
    print("=" * 50)
    
    test_old_version_download()

if __name__ == "__main__":
    main()
