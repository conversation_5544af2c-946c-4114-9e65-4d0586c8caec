# Excel文档文件名扩展名修复

## 🐛 问题描述

用户发现下载Excel表格时，文件扩展名错误地显示为`.docx`而不是`.xlsx`：

```
✅ downloads/知识库/无标题表格.docx  # 应该是 .xlsx
```

## 🔍 问题分析

问题出现在`yuque_downloader.py`中的文件名生成逻辑：

```python
# 原有代码 - 问题所在
filename = self.file_manager.generate_filename(doc_title)  # 没有传递doc_type参数
```

`generate_filename`方法的默认参数是`doc_type="word"`，导致所有文档都使用`.docx`扩展名：

```python
def generate_filename(self, doc_title: str, doc_type: str = "word") -> str:
    # 默认使用word类型，导致Excel文档也生成.docx扩展名
```

## ✅ 修复方案

在`yuque_downloader.py`中修改文件名生成逻辑，根据文档类型确定正确的导出类型：

### 修复前
```python
download_url = export_result.data

# 处理文档下载
filename = self.file_manager.generate_filename(doc_title)
file_path = book_dir / filename
```

### 修复后
```python
download_url = export_result.data

# 根据文档类型确定导出类型，生成正确的文件名
if doc_type.lower() == 'sheet':
    export_type = 'excel'
else:
    export_type = 'word'  # 默认使用word类型

filename = self.file_manager.generate_filename(doc_title, export_type)
file_path = book_dir / filename
```

## 🔧 技术细节

### 文档类型映射
- `Sheet` 类型文档 → `excel` 导出类型 → `.xlsx` 扩展名
- 其他类型文档 → `word` 导出类型 → `.docx` 扩展名

### 扩展名映射（在`file_manager.py`中）
```python
extension_map = {
    "word": ".docx",
    "pdf": ".pdf", 
    "markdown": ".md",
    "html": ".html",
    "excel": ".xlsx"  # Excel支持
}
```

## ✅ 测试验证

已通过完整测试验证修复效果：

### 基本测试
- ✅ Sheet类型文档 → `.xlsx`扩展名
- ✅ Doc类型文档 → `.docx`扩展名  
- ✅ 大小写不敏感（`sheet`/`Sheet`/`SHEET`）

### 边界测试
- ✅ 大写`SHEET`类型 → `.xlsx`扩展名
- ✅ 未知类型文档 → `.docx`扩展名（默认）
- ✅ 空类型文档 → `.docx`扩展名（默认）

## 📋 修复结果

修复后的文件名示例：

```
✅ downloads/知识库/无标题表格.xlsx     # 正确的Excel扩展名
✅ downloads/知识库/普通文档.docx      # Word文档保持不变
✅ downloads/知识库/数据统计表.xlsx    # Excel文档正确扩展名
```

## 🚀 影响范围

- **正面影响**: Excel文档现在有正确的文件扩展名，便于用户识别和打开
- **兼容性**: 不影响其他类型文档的文件名生成
- **用户体验**: 文件类型更加直观，符合用户预期

## 📁 修改文件

- `yuque_downloader.py` - 主要修复文件，添加了文档类型判断逻辑

## 🎯 修复状态

✅ **问题已完全修复**

现在Excel文档会正确生成`.xlsx`扩展名，用户可以直接通过文件扩展名识别文件类型。
