#!/usr/bin/env python3
"""
语雀下载工具分发包构建脚本
用于创建可分发的程序包
"""

import os
import shutil
import zipfile
import sys
from pathlib import Path
import time

def get_current_time():
    """获取当前时间戳"""
    return time.strftime("%Y%m%d_%H%M%S")

def clean_directory(path):
    """清理目录"""
    if os.path.exists(path):
        shutil.rmtree(path)
    os.makedirs(path, exist_ok=True)

def copy_core_files(dist_dir):
    """复制核心文件到分发目录"""
    # 核心Python文件
    core_files = [
        'main.py',
        'yuque_downloader.py', 
        'yuque_books_interceptor.py',
        'user_interface.py',
        'api_client.py',
        'file_manager.py',
        'yuque_webdriver_manager.py',
        'utils.py',
        'config.py',
        'path_manager.py',
        'api_result.py',
        'exceptions.py',
        'sync_auth.py',
        '__init__.py'
    ]
    
    print("📋 复制核心文件...")
    for file in core_files:
        if os.path.exists(file):
            shutil.copy2(file, dist_dir)
            print(f"   ✅ {file}")
        else:
            print(f"   ⚠️ 跳过不存在的文件: {file}")
    
    # 配置和说明文件
    config_files = [
        'requirements.txt',
        'README.md',
        'LICENSE',
        'INSTALL.md',
        'setup.py',
        'EXCEL_DOWNLOAD_SUMMARY.md',  # Excel功能说明
        'FILENAME_EXTENSION_FIX.md'   # 文件名修复说明
    ]
    
    print("\n📝 复制配置和文档文件...")
    for file in config_files:
        if os.path.exists(file):
            shutil.copy2(file, dist_dir)
            print(f"   ✅ {file}")

def create_startup_scripts(dist_dir):
    """创建启动脚本"""
    print("\n🚀 创建启动脚本...")
    
    # Windows批处理文件
    bat_content = '''@echo off
chcp 65001 >nul
echo [YUQUE DOWNLOADER] 语雀知识库批量下载工具
echo ===============================
echo.
echo [INFO] 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] 未找到Python，请先安装Python 3.8+
    echo [INFO] 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo [OK] Python环境正常
echo.
echo [INFO] 检查依赖包...
python -c "import selenium, seleniumwire, requests, tqdm" >nul 2>&1
if %errorlevel% neq 0 (
    echo [INFO] 正在安装依赖包...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo [ERROR] 依赖安装失败，请手动运行: pip install -r requirements.txt
        pause
        exit /b 1
    )
)

echo [OK] 依赖检查完成
echo.
echo [START] 启动程序...
echo ===============================
python main.py
pause
'''
    
    with open(os.path.join(dist_dir, 'start.bat'), 'w', encoding='utf-8') as f:
        f.write(bat_content)
    
    # Unix Shell脚本
    sh_content = '''#!/bin/bash
echo "🚀 语雀知识库批量下载工具"
echo "==============================="
echo

echo "🔧 检查Python环境..."
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo "❌ 错误: 未找到Python，请先安装Python 3.8+"
    echo "📥 安装指令:"
    echo "   macOS: brew install python"
    echo "   Ubuntu: sudo apt install python3 python3-pip"
    exit 1
fi

# 尝试使用python3，如果不存在则使用python
if command -v python3 &> /dev/null; then
    PYTHON_CMD=python3
    PIP_CMD=pip3
else
    PYTHON_CMD=python
    PIP_CMD=pip
fi

echo "✅ Python环境正常"
echo

echo "🔧 检查依赖包..."
$PYTHON_CMD -c "import selenium, seleniumwire, requests, tqdm" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "📦 正在安装依赖包..."
    $PIP_CMD install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败，请手动运行: $PIP_CMD install -r requirements.txt"
        exit 1
    fi
fi

echo "✅ 依赖检查完成"
echo

echo "🎯 启动程序..."
echo "==============================="
$PYTHON_CMD main.py
'''
    
    sh_file = os.path.join(dist_dir, 'start.sh')
    with open(sh_file, 'w', encoding='utf-8') as f:
        f.write(sh_content)
    
    # 设置执行权限
    os.chmod(sh_file, 0o755)
    
    print("   ✅ start.bat (Windows)")
    print("   ✅ start.sh (macOS/Linux)")

def create_directories(dist_dir):
    """创建必要的目录结构"""
    print("\n📁 创建目录结构...")
    
    dirs = ['drivers', 'downloads', 'logs']
    for dir_name in dirs:
        dir_path = os.path.join(dist_dir, dir_name)
        os.makedirs(dir_path, exist_ok=True)
        
        # 创建.gitkeep文件保持目录
        gitkeep_path = os.path.join(dir_path, '.gitkeep')
        with open(gitkeep_path, 'w') as f:
            f.write('')
        
        print(f"   ✅ {dir_name}/")

def create_user_guide(dist_dir):
    """创建用户快速指南"""
    print("\n📖 创建用户指南...")
    
    guide_content = '''# 🚀 语雀下载工具 v2.1.0 - 快速开始

## ✨ 新功能亮点 (v2.1.0)
- � **Excel文档自动下载**: 支持自动下载Excel表格文档(.xlsx)
- 🔧 **文件名修复**: Excel文档现在正确显示.xlsx扩展名
- 📊 **统一处理**: Excel文档与Word文档使用相同的下载流程

## �🎯 一键启动

### Windows用户
双击运行 `start.bat` 文件

### macOS/Linux用户
在终端中运行：
```bash
chmod +x start.sh
./start.sh
```

## 📋 使用流程

1. **首次启动**: 程序会自动检查和安装依赖
2. **浏览器登录**: 在打开的Chrome浏览器中登录语雀账号
3. **选择知识库**: 从拦截到的列表中选择要下载的知识库
4. **选择模式**:
   - 全部下载: 下载选中知识库的所有文档
   - 精选下载: 为每个知识库单独选择文档
5. **开始下载**: 确认后程序自动下载到 `downloads/` 目录

## 📊 支持的文档类型

- 📝 **Word文档** (.docx) - 普通文档和富文本内容
- 📊 **Excel表格** (.xlsx) - 数据表格和图表 ✨新增
- 📋 **其他格式** - 根据导出设置支持PDF、Markdown等

## 📁 文件说明

- `main.py` - 程序主入口
- `requirements.txt` - Python依赖列表
- `README.md` - 详细说明文档
- `INSTALL.md` - 安装指南
- `EXCEL_DOWNLOAD_SUMMARY.md` - Excel功能详细说明 ✨新增
- `FILENAME_EXTENSION_FIX.md` - 文件名修复说明 ✨新增
- `downloads/` - 下载文件存储目录
- `logs/` - 程序日志目录

## ❓ 常见问题

**Q: 浏览器无法启动？**
A: 确保已安装Chrome浏览器，程序会自动下载ChromeDriver

**Q: 权限错误？**  
A: Windows右键"以管理员身份运行"，macOS/Linux使用sudo

**Q: 网络错误？**
A: 检查网络连接，确保能访问yuque.com

## 📞 技术支持

- 查看详细文档: README.md
- 安装问题: INSTALL.md  
- 问题反馈: GitHub Issues

---
💡 提示: 首次使用建议阅读 README.md 获取完整功能说明
'''
    
    with open(os.path.join(dist_dir, 'QUICKSTART.md'), 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print("   ✅ QUICKSTART.md")

def create_zip_package(dist_dir, version="2.1.0"):
    """创建ZIP压缩包"""
    timestamp = get_current_time()
    zip_name = f"yuque-downloader-v{version}-{timestamp}.zip"
    
    print(f"\n📦 创建分发包: {zip_name}")
    
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(dist_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arcname = os.path.relpath(file_path, dist_dir)
                zipf.write(file_path, arcname)
                print(f"   📄 添加: {arcname}")
    
    # 获取文件大小
    size_mb = os.path.getsize(zip_name) / 1024 / 1024
    print(f"\n✅ 分发包创建成功!")
    print(f"📦 文件名: {zip_name}")
    print(f"📊 大小: {size_mb:.1f} MB")
    
    return zip_name

def verify_package(dist_dir):
    """验证包内容"""
    print("\n🔍 验证分发包内容...")
    
    required_files = [
        'main.py',
        'requirements.txt', 
        'README.md',
        'start.bat',
        'start.sh'
    ]
    
    missing_files = []
    for file in required_files:
        file_path = os.path.join(dist_dir, file)
        if os.path.exists(file_path):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file}")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n⚠️ 警告: 缺少关键文件: {', '.join(missing_files)}")
        return False
    else:
        print("\n✅ 包内容验证通过!")
        return True

def main():
    """主函数"""
    print("🏗️ 语雀下载工具分发包构建器")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists('main.py'):
        print("❌ 错误: 请在项目根目录运行此脚本")
        sys.exit(1)
    
    # 创建分发目录
    timestamp = get_current_time()
    dist_dir = f"yuque-downloader-dist-{timestamp}"
    
    print(f"📁 创建分发目录: {dist_dir}")
    clean_directory(dist_dir)
    
    try:
        # 构建分发包
        copy_core_files(dist_dir)
        create_startup_scripts(dist_dir)
        create_directories(dist_dir)
        create_user_guide(dist_dir)
        
        # 验证包内容
        if verify_package(dist_dir):
            # 创建ZIP包
            zip_file = create_zip_package(dist_dir)
            
            print("\n" + "=" * 50)
            print("🎉 分发包构建完成!")
            print("=" * 50)
            print(f"📦 分发包: {zip_file}")
            print(f"📁 源目录: {dist_dir}")
            print("\n📋 分发说明:")
            print("1. 将ZIP文件发送给用户")
            print("2. 用户解压后运行start.bat(Windows)或start.sh(macOS/Linux)")
            print("3. 程序会自动检查环境和安装依赖")
            
        else:
            print("\n❌ 包内容验证失败，请检查缺失文件")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ 构建过程发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 