# 语雀API客户端文档下载功能改进说明

## 改进概述

基于 `oldfile` 中的成功经验，对 `api_client.py` 中的文档下载功能进行了全面改进，实现了表格文档和普通文档的分离处理，避免下载错误和403权限问题。

## 主要改进内容

### 1. 文档导出请求分离

#### 原有问题
- 所有文档类型使用相同的导出参数
- 表格文档（Excel）和普通文档（Word/Markdown）混用导出配置
- 容易导致403错误或下载失败

#### 改进方案
将 `export_doc` 方法重构为分离式架构：

```python
def export_doc(self, doc_id: str, doc_type: str = None) -> APIResult:
    """导出文档并返回下载URL - 根据文档类型选择不同的导出策略"""
    if doc_type and doc_type.lower() == 'sheet':
        return self._export_sheet_doc(doc_id)
    else:
        return self._export_regular_doc(doc_id)
```

#### 新增方法

**1. `_export_regular_doc(doc_id)` - 普通文档导出**
- 使用Word格式导出 (`type: "word"`)
- 使用配置文件中的默认force参数
- 适用于Markdown、富文本等普通文档

**2. `_export_sheet_doc(doc_id)` - 表格文档导出**
- 使用Excel格式导出 (`type: "excel"`)
- 强制设置 `force: 0`（不强制导出）
- 专门处理表格/电子表格文档

### 2. 导出状态轮询分离

#### 新增专用轮询方法

**`_poll_sheet_export_status()` - 表格文档状态轮询**
- 专门处理Excel文档的导出状态检查
- 自动构建完整的Excel下载URL
- 处理表格文档特有的状态响应格式
- 支持直接URL获取和状态轮询两种模式

#### 关键特性
- 检测直接返回的下载URL
- 处理 `processing`、`success`、`failed` 等状态
- 自动调用 `_construct_excel_download_url()` 构建完整URL

### 3. 文件下载策略分离

#### 原有问题
- 所有文件类型使用相同的下载策略
- 没有针对Excel文件的特殊处理
- 容易遇到OSS权限问题

#### 改进方案

**1. 主下载方法改进**
```python
def download_file(self, url: str, file_path: str, doc_type: Optional[str] = None) -> bool:
    """根据文档类型选择下载策略"""
    if doc_type and doc_type.lower() == 'sheet':
        return self._download_sheet_file(url, file_path)
    else:
        return self._download_regular_file(url, file_path)
```

**2. `_download_regular_file()` - 普通文档下载**
- 使用标准的下载策略序列
- 直接下载 → 语雀认证头 → 完整认证

**3. `_download_sheet_file()` - 表格文档下载**
- 使用专门的表格文档下载策略序列
- 表格专用下载 → 语雀认证头 → 完整认证 → 直接下载

**4. `_download_sheet_with_special_headers()` - 表格专用下载**
- 使用Excel专用的Accept头：`application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`
- 处理302/301重定向
- 自动替换 `/attachments` 为OSS地址
- 添加Excel文档专用的安全头部

### 4. URL处理优化

#### Excel文档URL构建
- `_construct_excel_download_url()` 方法优化
- 自动检测相对URL并构建完整路径
- 支持企业版和公版语雀的URL构建
- 处理URL格式异常情况

#### 重定向处理
- 自动处理302/301重定向
- 智能替换 `/attachments` 路径为OSS地址
- 支持多级重定向跟踪

### 5. 错误处理和日志

#### 分类错误处理
- 普通文档和表格文档分别记录错误信息
- 详细的策略失败日志
- 区分不同类型文档的超时处理

#### 日志优化
- 明确标识文档类型（普通文档/表格文档）
- 记录使用的导出参数和下载策略
- 详细的URL构建和重定向日志

## 使用方法

### 导出文档
```python
# 普通文档导出
result = api_client.export_doc(doc_id="12345", doc_type="doc")

# 表格文档导出  
result = api_client.export_doc(doc_id="12345", doc_type="sheet")
```

### 下载文件
```python
# 普通文档下载
success = api_client.download_file(url, file_path, doc_type="doc")

# 表格文档下载
success = api_client.download_file(url, file_path, doc_type="sheet")
```

## 兼容性

- 保持与现有代码的向后兼容
- 如果不指定 `doc_type`，默认使用普通文档处理流程
- 所有原有的API接口保持不变

## 预期效果

1. **减少403错误**：通过分离的认证策略，避免权限问题
2. **提高下载成功率**：针对不同文档类型优化下载流程
3. **更好的错误诊断**：分类的错误信息便于问题定位
4. **提升稳定性**：专门的重定向和URL处理逻辑

## 技术要点

- 类型注解使用 `Optional[str]` 避免类型检查错误
- 保持原有的轮询机制和重试逻辑
- 继承原有的session管理和认证机制
- 兼容企业版和公版语雀环境
