#!/usr/bin/env python3
"""
语雀文档下载器主程序入口
使用原生selenium-wire进行请求拦截，支持会话记忆
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))


def check_webdriver_manager():
    """检查WebDriver管理器是否正确"""
    try:
        # 直接检查模块导入而不是文件读取
        from yuque_webdriver_manager import WebDriverManager
        
        # 检查关键方法是否存在
        if hasattr(WebDriverManager, 'initialize_driver') and hasattr(WebDriverManager, 'wait_for_login'):
            print("✅ WebDriver管理器版本正确")
            print("🔍 请求拦截功能已启用")
            print("💾 会话记忆功能已启用")
            print("🖼️ 图片显示功能已恢复")
            return True
        else:
            print("❌ WebDriver管理器版本不正确")
            return False

    except ImportError as e:
        print(f"❌ 检查WebDriver管理器失败: 无法导入模块 - {e}")
        return False
    except Exception as e:
        print(f"❌ 检查WebDriver管理器失败: {e}")
        return False


def check_dependencies():
    """检查依赖是否完整"""
    missing_deps = []
    
    try:
        import seleniumwire
        print("✅ selenium-wire 可用")
    except ImportError:
        missing_deps.append("selenium-wire")
    
    try:
        import selenium
        print("✅ selenium 可用")
    except ImportError:
        missing_deps.append("selenium")
    
    try:
        import requests
        print("✅ requests 可用")
    except ImportError:
        missing_deps.append("requests")
    
    try:
        import tqdm
        print("✅ tqdm 可用")
    except ImportError:
        missing_deps.append("tqdm")
    
    if missing_deps:
        print(f"❌ 缺少依赖包: {', '.join(missing_deps)}")
        print("请运行以下命令安装:")
        print("pip install 'urllib3<2.0' 'blinker<1.5' selenium-wire==5.1.0")
        return False
    
    return True


def show_session_info():
    """显示会话信息 - 简化版本"""
    session_files = [
        ("yuque_cookies.json", "登录状态"),
        ("yuque_headers.json", "请求头"), 
        ("yuque_session.json", "会话信息")
    ]
    
    # 检查是否有任何会话文件存在
    has_session = any(os.path.exists(file[0]) for file in session_files)
    
    if has_session:
        print("\n💾 会话状态: 发现已保存的会话信息")
        print("  ✅ 将尝试恢复之前的登录状态")
        # 只在详细模式下显示具体文件
        for file, desc in session_files:
            if os.path.exists(file):
                stat = os.stat(file)
                import time
                mtime = time.strftime('%m-%d %H:%M', time.localtime(stat.st_mtime))
                print(f"  📄 {desc} (上次保存: {mtime})")
    else:
        print("\n💾 会话状态: 首次运行，需要重新登录")
        print("  💡 登录后会自动保存会话信息，下次启动可跳过登录")


def main():
    """主函数"""
    print("🚀 语雀文档下载器启动")
    print("=" * 60)
    print("✨ 核心功能:")
    print("  📡 拦截器模式：自动拦截API获取书籍信息")
    print("  🔍 传统模式：使用selenium-wire进行请求拦截")
    print("  💾 支持会话记忆（cookies + headers + 请求历史）")
    print("  🖼️ 完整图片显示")
    print("  ⚡ 原生Selenium，稳定可靠")
    print("  📁 动态路径管理，便于部署")
    print("=" * 60)

    # 设置环境和路径
    print("\n🔧 初始化环境...")
    try:
        from path_manager import setup_environment, path_manager
        dynamic_config = setup_environment()
        path_manager.print_environment_info()
    except Exception as e:
        print(f"❌ 环境初始化失败: {e}")
        return

    # 检查依赖
    print("\n🔧 检查依赖...")
    if not check_dependencies():
        return

    # 显示会话信息
    show_session_info()

    # 检查WebDriver管理器版本
    print("\n🔄 检查WebDriver管理器...")
    if not check_webdriver_manager():
        print("❌ WebDriver管理器版本不正确")
        return
    
    try:
        # 导入并运行主程序
        from utils import setup_logging
        from yuque_downloader import YuqueDownloader
        from exceptions import YuqueException
        
        # 设置日志
        setup_logging()
        
        print("\n🎯 启动语雀文档下载器...")
        print("💡 提示: 支持多种获取书籍列表的方式")
        print("💡 提示: 拦截器模式可以自动获取最新书籍信息")
        print("💡 提示: 传统模式会自动拦截API请求并提取认证信息")
        print("💡 提示: 退出后会保存会话，下次启动时自动恢复")
        
        # 创建并运行下载器
        downloader = YuqueDownloader()
        downloader.run()
        
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序错误: {e}")
        print("请查看日志文件获取详细信息")
    finally:
        # 显示最终会话信息（简化版）
        session_files = ["yuque_cookies.json", "yuque_headers.json", "yuque_session.json"]
        saved_files = [f for f in session_files if os.path.exists(f)]
        
        if saved_files:
            print(f"\n💾 会话信息已保存 ({len(saved_files)}/3 个文件)")
            print("  ✅ 下次启动时可以跳过登录步骤")
        else:
            print("\n💾 未保存会话信息")
            print("  💡 下次启动需要重新登录")
        
        # 程序结束提示
        print("\n👋 程序已结束")


if __name__ == "__main__":
    main()
