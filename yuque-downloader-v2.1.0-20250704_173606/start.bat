@echo off
chcp 65001 >nul
echo [YUQUE DOWNLOADER] 语雀知识库批量下载工具
echo ===============================
echo.
echo [INFO] 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] 未找到Python，请先安装Python 3.8+
    echo [INFO] 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo [OK] Python环境正常
echo.
echo [INFO] 检查依赖包...
python -c "import selenium, seleniumwire, requests, tqdm" >nul 2>&1
if %errorlevel% neq 0 (
    echo [INFO] 正在安装依赖包...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo [ERROR] 依赖安装失败，请手动运行: pip install -r requirements.txt
        pause
        exit /b 1
    )
)

echo [OK] 依赖检查完成
echo.
echo [START] 启动程序...
echo ===============================
python main.py
pause
