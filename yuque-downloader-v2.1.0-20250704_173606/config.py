"""
语雀文档下载器配置文件
"""

# 语雀API相关配置
YUQUE_BASE_URL = "https://fjzx.yuque.com"  # 默认使用企业版语雀
YUQUE_API_BASE = "https://fjzx.yuque.com/api"
YUQUE_DASHBOARD_URL = "https://fjzx.yuque.com/dashboard/books"  # 知识库页面

# 备用配置（公版语雀）
YUQUE_PUBLIC_BASE_URL = "https://www.yuque.com"
YUQUE_PUBLIC_API_BASE = "https://www.yuque.com/api"
YUQUE_PUBLIC_DASHBOARD_URL = "https://www.yuque.com/dashboard/books"

# API端点
API_ENDPOINTS = {
    "book_stacks": "/mine/book_stacks",
    "user_books": "/mine/user_books",  # 新增：用户知识库列表
    "docs": "/docs",
    "export": "/docs/{doc_id}/export"
}

# WebDriver配置
WEBDRIVER_CONFIG = {
    "headless": False,  # 是否使用无头模式
    "window_size": (1920, 1080),
    "timeout": 30,  # 页面加载超时时间（秒）
    "implicit_wait": 10,  # 隐式等待时间（秒）
    "chrome_user_data_dir": "./chrome_user_data",  # Chrome用户数据目录（相对路径）
    "chromedriver_path": "./drivers/chromedriver"  # ChromeDriver可执行文件路径（相对路径）
}

# 下载配置
DOWNLOAD_CONFIG = {
    "export_type": "word",  # 导出类型
    "export_force": 0,  # 是否强制导出
    "poll_interval": 5,  # 轮询间隔（秒）
    "max_retries": 20,  # 最大重试次数
    "timeout": 300,  # 下载超时时间（秒）
    "chunk_size": 8192  # 下载块大小
}

# 文件管理配置
FILE_CONFIG = {
    "base_dir": "./downloads",  # 下载基础目录
    "invalid_chars": r'[<>:"/\\|?*]',  # 文件名非法字符
    "max_filename_length": 200  # 最大文件名长度
}

# 日志配置
LOG_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": "./logs/yuque_downloader.log",  # 修改：日志文件存放在logs目录下
    "max_size": 10 * 1024 * 1024,  # 10MB
    "backup_count": 5
}

# 重试配置
RETRY_CONFIG = {
    "max_attempts": 3,
    "delay": 1,  # 重试延迟（秒）
    "backoff": 2  # 退避倍数
}

# 控制台输出配置
CONSOLE_OUTPUT_CONFIG = {
    "show_api_status": True,  # 是否显示API请求状态
    "show_progress": True,   # 是否显示进度信息
    "show_detailed_errors": False,  # 是否显示详细错误信息
    "api_status_format": "[API] {method} {endpoint} -> {status}",  # API状态显示格式
    "console_log_level": "WARNING"  # 控制台日志级别
}


