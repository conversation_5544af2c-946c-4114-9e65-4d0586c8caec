"""
文件管理模块
负责文件下载、目录创建和文件命名管理
"""

import os
import hashlib
import logging
from pathlib import Path
from typing import Optional, Dict, Any
from urllib.parse import urlparse
from config import FILE_CONFIG, DOWNLOAD_CONFIG
from utils import sanitize_filename, format_file_size, ensure_directory_exists, retry_on_failure

logger = logging.getLogger(__name__)


class FileManager:
    """文件管理器"""
    
    def __init__(self, base_dir: Optional[str] = None):
        self.base_dir = Path(base_dir or FILE_CONFIG["base_dir"])
        self.downloaded_files = {}
        self.failed_downloads = []

        # 确保基础目录存在
        ensure_directory_exists(str(self.base_dir))



    def create_book_directory(self, book_name: str) -> Path:
        """为知识库创建目录"""
        safe_book_name = sanitize_filename(book_name, FILE_CONFIG["max_filename_length"])
        book_dir = self.base_dir / safe_book_name
        
        try:
            book_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"创建知识库目录: {book_dir}")
            return book_dir
        except Exception as e:
            logger.error(f"创建知识库目录失败 {book_name}: {e}")
            raise
    
    def generate_filename(self, doc_title: str, doc_type: str = "word") -> str:
        """生成安全的文件名"""
        # 清理文档标题
        safe_title = sanitize_filename(doc_title, FILE_CONFIG["max_filename_length"] - 10)

        # 根据导出类型确定扩展名
        extension_map = {
            "word": ".docx",
            "pdf": ".pdf",
            "markdown": ".md",
            "html": ".html",
            "excel": ".xlsx"  # 添加Excel支持
        }

        extension = extension_map.get(doc_type.lower(), ".docx")
        filename = safe_title + extension

        return filename
    

    
    def handle_duplicate_filename(self, file_path: Path, new_content_url: Optional[str] = None, api_client=None) -> tuple[Path, str]:
        """智能处理重复文件名

        Returns:
            tuple: (final_file_path, action)
            action可以是: 'new', 'skip_duplicate', 'save_version'
        """
        if not self.check_file_exists(file_path):
            return file_path, 'new'

        # 如果提供了新内容URL和API客户端，比较文件内容
        if new_content_url and api_client:
            logger.info(f"检测到重复文件，正在比较内容: {file_path.name}")

            # 下载新文件到临时位置进行比较
            temp_file = file_path.parent / f"temp_{file_path.name}"
            try:
                # 下载新文件到临时位置
                if api_client.download_file(new_content_url, str(temp_file)):
                    # 比较文件内容
                    if self._compare_file_contents(file_path, temp_file):
                        # 内容相同，删除临时文件，跳过下载
                        temp_file.unlink()
                        logger.info(f"文件内容相同，跳过下载: {file_path.name}")
                        print(f"⏭️ 跳过重复文件: {file_path.name} (内容相同)")
                        return file_path, 'skip_duplicate'
                    else:
                        # 内容不同，保存为新版本
                        temp_file.unlink()  # 删除临时文件
                        new_path = self._generate_version_filename(file_path)
                        logger.info(f"文件内容不同，保存为新版本: {new_path.name}")
                        print(f"📄 保存新版本: {new_path.name} (内容不同)")
                        return new_path, 'save_version'
                else:
                    # 下载失败，使用原有逻辑
                    logger.warning(f"无法下载新文件进行比较，使用原有逻辑: {file_path.name}")
                    if temp_file.exists():
                        temp_file.unlink()
            except Exception as e:
                logger.error(f"比较文件内容时出错: {e}")
                if temp_file.exists():
                    temp_file.unlink()

        # 原有逻辑：生成新的文件名
        new_path = self._generate_version_filename(file_path)
        logger.info(f"文件已存在，使用新名称: {new_path.name}")
        return new_path, 'save_version'

    def _compare_file_contents(self, file1: Path, file2: Path) -> bool:
        """比较两个文件的内容是否相同"""
        try:
            # 首先比较文件大小
            if file1.stat().st_size != file2.stat().st_size:
                return False

            # 比较文件哈希值
            hash1 = self.calculate_file_hash(file1)
            hash2 = self.calculate_file_hash(file2)

            if hash1 and hash2:
                return hash1 == hash2

            # 如果哈希计算失败，逐字节比较
            with open(file1, 'rb') as f1, open(file2, 'rb') as f2:
                while True:
                    chunk1 = f1.read(4096)
                    chunk2 = f2.read(4096)
                    if chunk1 != chunk2:
                        return False
                    if not chunk1:  # 到达文件末尾
                        break
            return True

        except Exception as e:
            logger.error(f"比较文件内容失败: {e}")
            return False

    def _generate_version_filename(self, file_path: Path) -> Path:
        """生成版本化的文件名"""
        stem = file_path.stem
        suffix = file_path.suffix
        parent = file_path.parent

        counter = 2  # 从v2开始
        while True:
            new_name = f"{stem}_v{counter}{suffix}"
            new_path = parent / new_name

            if not self.check_file_exists(new_path):
                return new_path

            counter += 1
            if counter > 100:  # 防止无限循环
                raise Exception(f"无法生成唯一文件名: {file_path}")
    
    @retry_on_failure(max_attempts=3)
    def download_and_save(self, url: str, file_path: Path, api_client) -> tuple[bool, str]:
        """下载并保存文件"""
        try:
            logger.info(f"开始下载文件: {file_path.name}")

            # 确保目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)

            # 检查文件是否已存在（通过内容比较）
            if self.check_file_exists(file_path, url, api_client):
                return True, 'skipped'  # 文件已存在且内容相同，跳过下载

            # 智能处理重复文件名
            final_path, action = self.handle_duplicate_filename(file_path, url, api_client)

            # 如果是跳过重复文件，直接返回成功
            if action == 'skip_duplicate':
                self.downloaded_files[str(final_path)] = {
                    'url': url,
                    'size': final_path.stat().st_size,
                    'path': str(final_path),
                    'status': 'skipped_duplicate'
                }
                return True, 'skipped'

            # 下载文件
            success = api_client.download_file(url, str(final_path))

            if success and self.verify_download(final_path):
                status = 'downloaded'
                if action == 'save_version':
                    status = 'new_version'

                self.downloaded_files[str(final_path)] = {
                    'url': url,
                    'size': final_path.stat().st_size,
                    'path': str(final_path),
                    'status': status
                }
                logger.info(f"文件下载成功: {final_path}")
                return True, 'downloaded'
            else:
                # 记录失败但不抛出异常
                error_msg = f"文件下载失败或验证失败: {final_path}"
                logger.error(error_msg)
                self.failed_downloads.append({
                    'path': str(final_path),
                    'url': url,
                    'error': error_msg
                })
                return False, 'failed'

        except Exception as e:
            # 记录异常但不重新抛出
            error_msg = f"下载文件异常 {url}: {e}"
            logger.error(error_msg)
            self.failed_downloads.append({
                'path': str(file_path),
                'url': url,
                'error': error_msg
            })
            return False, 'failed'

    def check_file_exists(self, file_path: Path, url: Optional[str] = None, api_client=None) -> bool:
        """检查文件是否已存在，通过内容比较确定是否跳过下载"""
        if not file_path.exists():
            return False

        file_size = file_path.stat().st_size
        if file_size == 0:
            # 如果是空文件，删除它并重新下载
            logger.warning(f"发现空文件，将重新下载: {file_path}")
            try:
                file_path.unlink()
            except Exception as e:
                logger.error(f"删除空文件失败: {e}")
            return False

        # 如果提供了URL和API客户端，进行内容比较
        if url and api_client:
            logger.info(f"检测到已存在文件，正在比较内容: {file_path.name}")

            # 下载新文件到临时位置进行比较
            temp_file = file_path.parent / f"temp_check_{file_path.name}"
            try:
                # 下载新文件到临时位置
                if api_client.download_file(url, str(temp_file)):
                    # 比较文件内容
                    if self._compare_file_contents(file_path, temp_file):
                        # 内容相同，删除临时文件，跳过下载
                        temp_file.unlink()
                        logger.info(f"文件内容相同，跳过下载: {file_path.name}")
                        print(f"⏭️ 跳过已存在文件: {file_path.name} (内容相同)")

                        # 记录为已下载文件
                        self.downloaded_files[str(file_path)] = {
                            'url': url,
                            'size': file_size,
                            'path': str(file_path),
                            'status': 'skipped_same_content'
                        }
                        return True
                    else:
                        # 内容不同，删除临时文件，需要重新下载
                        temp_file.unlink()
                        logger.info(f"文件内容不同，需要重新下载: {file_path.name}")
                        print(f"🔄 文件内容不同，将重新下载: {file_path.name}")
                        return False
                else:
                    # 下载失败，无法比较，保守起见跳过
                    logger.warning(f"无法下载新文件进行比较，跳过下载: {file_path.name}")
                    print(f"⏭️ 跳过已存在文件: {file_path.name} (无法比较内容)")

                    # 记录为已下载文件
                    self.downloaded_files[str(file_path)] = {
                        'url': url or 'unknown',
                        'size': file_size,
                        'path': str(file_path),
                        'status': 'skipped_no_compare'
                    }
                    return True

            except Exception as e:
                logger.error(f"比较文件内容时出错: {e}")
                if temp_file.exists():
                    temp_file.unlink()
                # 出错时保守跳过
                print(f"⏭️ 跳过已存在文件: {file_path.name} (比较出错)")
                self.downloaded_files[str(file_path)] = {
                    'url': url or 'unknown',
                    'size': file_size,
                    'path': str(file_path),
                    'status': 'skipped_error'
                }
                return True
        else:
            # 没有提供URL或API客户端，直接跳过
            print(f"⏭️ 跳过已存在文件: {file_path.name}")
            logger.info(f"跳过已存在文件: {file_path}")

            # 记录为已下载文件
            self.downloaded_files[str(file_path)] = {
                'url': 'skipped',
                'size': file_size,
                'path': str(file_path),
                'status': 'skipped'
            }
            return True
    
    def verify_download(self, file_path: Path) -> bool:
        """验证下载的文件"""
        try:
            if not file_path.exists():
                logger.error(f"文件不存在: {file_path}")
                return False
            
            file_size = file_path.stat().st_size
            if file_size == 0:
                logger.error(f"文件大小为0: {file_path}")
                return False
            
            # 检查文件是否为有效的文档文件
            if file_path.suffix.lower() in ['.docx', '.doc']:
                return self._verify_word_document(file_path)
            elif file_path.suffix.lower() == '.pdf':
                return self._verify_pdf_document(file_path)
            
            # 对于其他类型，只检查大小
            logger.info(f"文件验证通过: {file_path} ({format_file_size(file_size)})")
            return True
            
        except Exception as e:
            logger.error(f"文件验证失败 {file_path}: {e}")
            return False
    
    def _verify_word_document(self, file_path: Path) -> bool:
        """验证Word文档"""
        try:
            # 检查文件头
            with open(file_path, 'rb') as f:
                header = f.read(4)
                # Word文档的魔数
                if header == b'PK\x03\x04':  # ZIP格式 (docx)
                    logger.info(f"Word文档验证通过: {file_path}")
                    return True
                elif header[:2] == b'\xd0\xcf':  # OLE格式 (doc)
                    logger.info(f"Word文档验证通过: {file_path}")
                    return True
                else:
                    logger.warning(f"Word文档格式可能异常: {file_path}")
                    return True  # 仍然认为有效，可能是其他格式
        except Exception as e:
            logger.error(f"Word文档验证失败 {file_path}: {e}")
            return False
    
    def _verify_pdf_document(self, file_path: Path) -> bool:
        """验证PDF文档"""
        try:
            with open(file_path, 'rb') as f:
                header = f.read(5)
                if header == b'%PDF-':
                    logger.info(f"PDF文档验证通过: {file_path}")
                    return True
                else:
                    logger.warning(f"PDF文档格式异常: {file_path}")
                    return False
        except Exception as e:
            logger.error(f"PDF文档验证失败 {file_path}: {e}")
            return False
    
    def calculate_file_hash(self, file_path: Path) -> Optional[str]:
        """计算文件哈希值"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希失败 {file_path}: {e}")
            return None
    
    def cleanup_failed_downloads(self):
        """清理失败的下载文件"""
        for failed_item in self.failed_downloads:
            try:
                # 处理字典格式的失败记录
                if isinstance(failed_item, dict):
                    file_path = failed_item.get('path', '')
                else:
                    # 兼容旧格式的字符串路径
                    file_path = str(failed_item)

                if file_path:
                    path = Path(file_path)
                    if path.exists() and path.stat().st_size == 0:
                        path.unlink()
                        logger.info(f"清理空文件: {file_path}")
            except Exception as e:
                logger.error(f"清理文件失败 {failed_item}: {e}")
    
    def get_download_summary(self) -> Dict[str, Any]:
        """获取下载摘要"""
        downloaded_files = []
        skipped_files = []
        duplicate_files = []
        total_size = 0

        for path, info in self.downloaded_files.items():
            status = info.get('status', 'downloaded')
            if status in ['skipped', 'skipped_duplicate', 'skipped_same_content', 'skipped_no_compare', 'skipped_error']:
                if status == 'skipped_duplicate':
                    duplicate_files.append(path)
                else:
                    skipped_files.append(path)
            else:
                downloaded_files.append(path)
            total_size += info['size']

        return {
            'total_files': len(self.downloaded_files),
            'downloaded_files': len(downloaded_files),
            'skipped_files': len(skipped_files),
            'duplicate_files': len(duplicate_files),
            'failed_files': len(self.failed_downloads),
            'total_size': total_size,
            'total_size_formatted': format_file_size(total_size),
            'downloaded_file_list': downloaded_files,
            'skipped_file_list': skipped_files,
            'duplicate_file_list': duplicate_files,
            'failed_file_list': self.failed_downloads
        }
    
    def export_download_log(self, log_file: Optional[str] = None):
        """导出下载日志"""
        if log_file is None:
            from utils import get_timestamp
            log_file = f"download_log_{get_timestamp()}.txt"

        try:
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write("语雀文档下载日志\n")
                f.write("=" * 50 + "\n\n")

                summary = self.get_download_summary()
                f.write(f"下载摘要:\n")
                f.write(f"  成功下载: {summary['downloaded_files']} 个文件\n")
                f.write(f"  跳过文件: {summary['skipped_files']} 个文件\n")
                f.write(f"  重复文件: {summary['duplicate_files']} 个文件\n")
                f.write(f"  下载失败: {summary['failed_files']} 个文件\n")
                f.write(f"  总大小: {summary['total_size_formatted']}\n\n")

                # 成功下载的文件
                if summary['downloaded_file_list']:
                    f.write("成功下载的文件:\n")
                    for file_path in summary['downloaded_file_list']:
                        f.write(f"  ✅ {file_path}\n")
                    f.write("\n")

                # 跳过的文件
                if summary['skipped_file_list']:
                    f.write("跳过的文件:\n")
                    for file_path in summary['skipped_file_list']:
                        f.write(f"  ⏭️ {file_path}\n")
                    f.write("\n")

                # 重复文件（内容相同）
                if summary['duplicate_file_list']:
                    f.write("跳过的重复文件（内容相同）:\n")
                    for file_path in summary['duplicate_file_list']:
                        f.write(f"  🔄 {file_path}\n")
                    f.write("\n")



                # 下载失败的文件
                if summary['failed_file_list']:
                    f.write("下载失败的文件:\n")
                    for failed_item in summary['failed_file_list']:
                        if isinstance(failed_item, dict):
                            file_path = failed_item.get('path', '未知文件')
                            error = failed_item.get('error', '未知错误')
                            f.write(f"  ❌ {file_path}\n")
                            f.write(f"     错误原因: {error}\n")
                        else:
                            f.write(f"  ❌ {failed_item}\n")
                    f.write("\n")

            logger.info(f"下载日志已导出: {log_file}")
            print(f"📄 下载日志已导出: {log_file}")

        except Exception as e:
            logger.error(f"导出下载日志失败: {e}")
            print(f"❌ 导出下载日志失败: {e}")
