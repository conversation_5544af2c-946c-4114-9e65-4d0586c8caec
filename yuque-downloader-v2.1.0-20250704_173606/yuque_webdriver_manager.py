"""
WebDriver管理模块
负责Chrome WebDriver的初始化、配置和管理
"""

import os
import json
import time
import logging
from typing import Optional, Dict
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from seleniumwire import webdriver as wire_webdriver
from webdriver_manager.chrome import ChromeDriverManager
from config import WEBDRIVER_CONFIG, YUQUE_BASE_URL
from path_manager import get_dynamic_config

logger = logging.getLogger(__name__)


class WebDriverManager:
    """WebDriver管理器"""
    
    def __init__(self):
        self.driver: Optional[wire_webdriver.Chrome] = None
        self.wait: Optional[WebDriverWait] = None
        self.cookies_file = "yuque_cookies.json"
        self.headers_file = "yuque_headers.json"
        self.captured_headers: Dict[str, str] = {}
        
    def initialize_driver(self) -> wire_webdriver.Chrome:
        """初始化Chrome WebDriver"""
        try:
            # 获取动态配置
            dynamic_config = get_dynamic_config()

            # Chrome选项配置
            chrome_options = Options()

            if WEBDRIVER_CONFIG["headless"]:
                chrome_options.add_argument("--headless")

            # 基础配置
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--disable-web-security")
            chrome_options.add_argument("--allow-running-insecure-content")
            chrome_options.add_argument(f"--window-size={WEBDRIVER_CONFIG['window_size'][0]},{WEBDRIVER_CONFIG['window_size'][1]}")

            # 极速启动优化参数
            chrome_options.add_argument("--disable-background-timer-throttling")
            chrome_options.add_argument("--disable-backgrounding-occluded-windows")
            chrome_options.add_argument("--disable-renderer-backgrounding")
            chrome_options.add_argument("--disable-background-networking")
            chrome_options.add_argument("--disable-default-apps")
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-sync")
            chrome_options.add_argument("--disable-translate")
            chrome_options.add_argument("--hide-scrollbars")
            chrome_options.add_argument("--metrics-recording-only")
            chrome_options.add_argument("--mute-audio")
            chrome_options.add_argument("--no-first-run")
            chrome_options.add_argument("--safebrowsing-disable-auto-update")
            chrome_options.add_argument("--disable-ipc-flooding-protection")
            chrome_options.add_argument("--disable-background-downloads")
            chrome_options.add_argument("--disable-component-update")
            chrome_options.add_argument("--disable-domain-reliability")
            chrome_options.add_argument("--disable-features=TranslateUI")
            chrome_options.add_argument("--disable-features=BlinkGenPropertyTrees")
            chrome_options.add_argument("--aggressive-cache-discard")

            # 新增极速启动参数
            chrome_options.add_argument("--disable-plugins")
            chrome_options.add_argument("--disable-plugins-discovery")
            chrome_options.add_argument("--disable-preconnect")
            chrome_options.add_argument("--disable-prefetch")
            chrome_options.add_argument("--disable-prefs-media-device-id-salt")
            chrome_options.add_argument("--disable-print-preview")
            chrome_options.add_argument("--disable-prompt-on-repost")
            chrome_options.add_argument("--disable-renderer-accessibility")
            chrome_options.add_argument("--disable-speech-api")
            chrome_options.add_argument("--disable-web-resources")
            chrome_options.add_argument("--disable-client-side-phishing-detection")
            chrome_options.add_argument("--disable-datasaver-prompt")
            chrome_options.add_argument("--disable-desktop-notifications")
            chrome_options.add_argument("--disable-device-discovery-notifications")
            chrome_options.add_argument("--disable-dinosaur-easter-egg")
            chrome_options.add_argument("--disable-features=VizDisplayCompositor,AudioServiceOutOfProcess")
            chrome_options.add_argument("--disable-hang-monitor")
            chrome_options.add_argument("--disable-logging")
            chrome_options.add_argument("--disable-login-animations")
            chrome_options.add_argument("--disable-notifications")
            chrome_options.add_argument("--disable-password-generation")
            chrome_options.add_argument("--disable-permissions-api")
            chrome_options.add_argument("--disable-popup-blocking")
            chrome_options.add_argument("--disable-presentation-api")
            chrome_options.add_argument("--disable-reading-from-canvas")
            chrome_options.add_argument("--disable-remote-fonts")
            chrome_options.add_argument("--disable-shared-workers")
            chrome_options.add_argument("--disable-software-rasterizer")
            chrome_options.add_argument("--disable-threaded-animation")
            chrome_options.add_argument("--disable-threaded-scrolling")
            chrome_options.add_argument("--disable-webgl")
            chrome_options.add_argument("--disable-webgl2")

            # 内存和性能优化
            chrome_options.add_argument("--memory-pressure-off")
            chrome_options.add_argument("--max_old_space_size=2048")  # 减少内存使用
            chrome_options.add_argument("--js-flags=--max-old-space-size=2048")
            chrome_options.add_argument("--force-device-scale-factor=1")
            chrome_options.add_argument("--high-dpi-support=1")
            chrome_options.add_argument("--ignore-gpu-blacklist")
            chrome_options.add_argument("--ignore-gpu-blocklist")

            # 配置Chrome用户数据目录以保持持久会话（使用动态路径）
            user_data_dir = dynamic_config["chrome_user_data_dir"]
            chrome_options.add_argument(f"--user-data-dir={user_data_dir}")
            logger.info(f"使用Chrome用户数据目录: {user_data_dir}")

            # 启用图片加载以提供更好的用户体验（根据用户偏好）
            prefs = {
                "profile.managed_default_content_settings.images": 1,  # 启用图片加载
                "profile.default_content_setting_values.notifications": 2
            }
            chrome_options.add_experimental_option("prefs", prefs)

            # 使用动态ChromeDriver路径
            chromedriver_path = dynamic_config["chromedriver_path"]

            # 检查ChromeDriver是否存在
            if not os.path.exists(chromedriver_path):
                logger.warning(f"ChromeDriver不存在于指定路径: {chromedriver_path}")
                try:
                    # 回退到ChromeDriverManager
                    logger.info("尝试使用ChromeDriverManager下载ChromeDriver...")
                    chromedriver_path = ChromeDriverManager().install()
                    logger.info(f"使用下载的ChromeDriver: {chromedriver_path}")
                except Exception as e:
                    logger.error(f"ChromeDriver初始化失败: {e}")
                    raise
            else:
                logger.info(f"使用本地ChromeDriver: {chromedriver_path}")

            service = Service(chromedriver_path)

            self.driver = wire_webdriver.Chrome(
                service=service,
                options=chrome_options
            )
            
            # 设置超时
            self.driver.implicitly_wait(WEBDRIVER_CONFIG["implicit_wait"])
            self.driver.set_page_load_timeout(WEBDRIVER_CONFIG["timeout"])
            
            # 创建WebDriverWait实例
            self.wait = WebDriverWait(self.driver, WEBDRIVER_CONFIG["timeout"])

            # 直接导航到知识库列表页面
            logger.info("导航到企业版语雀知识库页面...")
            from config import YUQUE_DASHBOARD_URL
            self.driver.get(YUQUE_DASHBOARD_URL)

            # 检查是否需要登录
            self._check_and_handle_login()

            logger.info("WebDriver初始化成功")
            return self.driver
            
        except Exception as e:
            logger.error(f"WebDriver初始化失败: {e}")
            raise

    def _check_and_handle_login(self):
        """检查并处理登录状态 - 优化版：快速检查，减少等待"""
        try:
            # 快速等待页面基本加载
            time.sleep(1)  # 从3秒减少到1秒

            current_url = self.driver.current_url
            logger.info(f"当前页面URL: {current_url}")

            # 快速检查是否被重定向到登录页面
            if 'login' in current_url.lower():
                print("\n" + "="*60)
                print("🔐 检测到需要登录")
                print("="*60)
                from config import YUQUE_DASHBOARD_URL
                print(f"请在浏览器中完成登录，然后访问 {YUQUE_DASHBOARD_URL} 页面")
                print("登录完成后，程序将自动继续...")
                print("="*60)
                print("⏳ 正在等待登录...")

                # 等待用户登录并导航到正确页面
                if not self._wait_for_correct_page():
                    print("❌ 登录等待超时，程序将退出")
                    raise Exception("登录超时")

            # 快速检查页面内容是否显示登录界面
            elif self._is_login_page_fast():
                print("\n" + "="*60)
                print("🔐 检测到登录界面")
                print("="*60)
                from config import YUQUE_DASHBOARD_URL
                print(f"请在浏览器中完成登录，然后访问 {YUQUE_DASHBOARD_URL} 页面")
                print("登录完成后，程序将自动继续...")
                print("="*60)
                print("⏳ 正在等待登录...")

                # 等待用户登录并导航到正确页面
                if not self._wait_for_correct_page():
                    print("❌ 登录等待超时，程序将退出")
                    raise Exception("登录超时")

            else:
                logger.info("页面状态正常，无需登录")
                print("✅ 页面状态正常，继续执行...")

        except Exception as e:
            logger.error(f"检查登录状态失败: {e}")
            raise

    def _is_login_page(self) -> bool:
        """检查当前页面是否为登录页面"""
        try:
            # 检查是否有登录相关元素
            login_indicators = [
                "//input[@type='password']",
                "//button[contains(text(), '登录')]",
                "//a[contains(text(), '登录')]",
                "//div[contains(@class, 'login')]"
            ]

            for xpath in login_indicators:
                try:
                    elements = self.driver.find_elements(By.XPATH, xpath)
                    if elements:
                        return True
                except:
                    pass

            return False

        except Exception as e:
            logger.error(f"检查登录页面失败: {e}")
            return False

    def _is_login_page_fast(self) -> bool:
        """快速检查当前页面是否为登录页面 - 优化版"""
        try:
            # 使用更快的检查方式
            page_source = self.driver.page_source
            
            # 确保page_source不为None
            if page_source is None:
                logger.warning("页面源码为空，回退到原始检查方法")
                return self._is_login_page()

            # 检查页面源码中的关键词
            login_keywords = ['登录', 'login', 'password', 'signin', 'sign-in']
            dashboard_keywords = ['dashboard', 'books', '知识库']
            
            # 转换为小写以便检查
            page_source_lower = page_source.lower()

            # 如果包含dashboard关键词，很可能不是登录页
            for keyword in dashboard_keywords:
                if keyword in page_source_lower:
                    return False

            # 检查是否包含登录关键词
            login_count = 0
            for keyword in login_keywords:
                if keyword in page_source_lower:
                    login_count += 1

            # 如果登录关键词出现次数较多，可能是登录页
            return login_count >= 2

        except Exception as e:
            logger.error(f"快速检查登录页面失败: {e}")
            # 回退到原始方法
            return self._is_login_page()

    def _wait_for_correct_page(self):
        """等待用户导航到正确页面"""
        from config import YUQUE_DASHBOARD_URL
        target_url = YUQUE_DASHBOARD_URL
        max_wait_time = 120  # 减少到2分钟
        start_time = time.time()
        check_interval = 2  # 每2秒检查一次，提高响应速度

        while time.time() - start_time < max_wait_time:
            current_url = self.driver.current_url

            # 检查是否在正确页面
            if 'dashboard/books' in current_url and not self._is_login_page():
                print("✅ 检测到已成功登录并导航到知识库页面")
                return True

            # 每2秒检查一次
            time.sleep(check_interval)

            # 每10秒提示一次
            elapsed = time.time() - start_time
            if int(elapsed) % 10 == 0 and elapsed > 0:
                print(f"⏳ 等待登录中... ({int(elapsed)}/{max_wait_time}秒)")

            # 如果等待超过30秒，提供额外提示
            if elapsed > 30 and int(elapsed) % 20 == 0:
                print("💡 提示：请确保在浏览器中完成登录并访问知识库页面")

        print(f"❌ 等待登录超时 ({max_wait_time}秒)")
        logger.warning("等待登录超时")
        return False

    def load_cookies(self) -> bool:
        """加载保存的cookies"""
        if not os.path.exists(self.cookies_file):
            return False

        try:
            with open(self.cookies_file, 'r', encoding='utf-8') as f:
                cookies = json.load(f)

            # 确保在语雀域名下加载cookies
            current_url = self.driver.current_url
            if 'yuque.com' not in current_url:
                # 如果不在语雀域名，先访问知识库页面
                from config import YUQUE_DASHBOARD_URL
                self.driver.get(YUQUE_DASHBOARD_URL)
                time.sleep(2)

            # 添加cookies
            for cookie in cookies:
                try:
                    self.driver.add_cookie(cookie)
                except Exception as e:
                    logger.warning(f"添加cookie失败: {e}")

            # 刷新页面使cookies生效
            self.driver.refresh()
            time.sleep(3)

            logger.info("Cookies加载成功")
            return True

        except Exception as e:
            logger.error(f"加载cookies失败: {e}")
            return False
    
    def save_cookies(self) -> bool:
        """保存当前cookies"""
        try:
            cookies = self.driver.get_cookies()
            with open(self.cookies_file, 'w', encoding='utf-8') as f:
                json.dump(cookies, f, ensure_ascii=False, indent=2)
            
            logger.info("Cookies保存成功")
            return True
            
        except Exception as e:
            logger.error(f"保存cookies失败: {e}")
            return False
    
    def check_login_status(self) -> bool:
        """检查登录状态 - 支持多域名"""
        try:
            current_url = self.driver.current_url
            logger.info(f"当前页面URL: {current_url}")

            # 检查是否在登录相关页面
            if self._is_login_or_home_page(current_url):
                logger.info("用户未登录 - 当前在登录页面或首页")
                return False

            # 如果不在登录页面，则认为已登录
            logger.info("用户已登录 - 不在登录页面或首页")
            return True

        except Exception as e:
            logger.error(f"检查登录状态失败: {e}")
            return False

    def _is_login_or_home_page(self, url: str) -> bool:
        """检查是否在登录页面或首页"""
        try:
            # 登录相关页面的URL模式
            login_patterns = [
                '/login',
                'yuque.com/',  # 首页
                'fjzx.yuque.com/',  # 企业版首页
                'yuque.com/login',
                'fjzx.yuque.com/login'
            ]

            # 检查URL是否匹配登录页面模式
            for pattern in login_patterns:
                if pattern in url:
                    # 进一步检查，确保不是其他包含这些关键词的页面
                    if url.endswith(pattern) or url.endswith(pattern + '/'):
                        return True
                    # 检查是否是纯首页（没有其他路径）
                    if pattern in ['yuque.com/', 'fjzx.yuque.com/']:
                        # 提取域名后的路径
                        if 'yuque.com/' in url:
                            path_after_domain = url.split('yuque.com/')[-1]
                            # 如果路径为空或只有查询参数，认为是首页
                            if not path_after_domain or path_after_domain.startswith('?'):
                                return True

            return False

        except Exception as e:
            logger.error(f"检查登录页面失败: {e}")
            return False
    
    def wait_for_login(self) -> bool:
        """等待用户手动登录 - 实时检测优化版"""
        print("\n🔐 请在浏览器中登录语雀账号...")
        print("💡 提示：登录完成后程序将自动检测并继续")
        print("⏸️  按 Ctrl+C 可随时中断等待")
        print("🚀 快速检测：登录完成后可按任意键立即检测")
        print("🔄 正在实时检测登录状态...")
        
        # 等待登录完成
        max_wait_time = 300  # 5分钟
        start_time = time.time()
        check_count = 0
        
        try:
            while time.time() - start_time < max_wait_time:
                check_count += 1
                elapsed = int(time.time() - start_time)
                
                # 每10次检查显示一次进度（每20秒）
                if check_count % 10 == 1:
                    remaining = max_wait_time - elapsed
                    print(f"⏰ 等待中... 已等待 {elapsed}s / 剩余 {remaining}s (每2秒检测一次)")
                
                # 检查登录状态
                if self.check_login_status():
                    print("✅ 登录成功！正在保存会话信息...")
                    self.save_cookies()
                    print("💾 会话信息已保存，继续程序...")
                    return True
                
                # 减少等待间隔，提高响应速度
                time.sleep(2)  # 从5秒减少到2秒
                
        except KeyboardInterrupt:
            print("\n\n⚠️ 用户中断等待登录")
            print("💡 提示：如需继续，请重新运行程序")
            return False
        
        print(f"\n❌ 等待登录超时 ({max_wait_time}秒)")
        print("💡 提示：请检查网络连接或重新运行程序")
        logger.error("等待登录超时")
        return False
    
    def get_intercepted_requests(self, url_pattern: str) -> list:
        """获取拦截的网络请求"""
        requests = []
        for request in self.driver.requests:
            if url_pattern in request.url and request.response:
                requests.append(request)
        return requests


    
    def clear_requests(self):
        """清空请求历史"""
        del self.driver.requests

    def capture_request_headers(self) -> Dict[str, str]:
        """捕获用户登录后的完整HTTP请求头"""
        try:
            print("   🔍 正在捕获请求头信息...")
            
            # 等待页面加载完成
            time.sleep(3)

            # 查找语雀API请求
            yuque_requests = []
            for request in self.driver.requests:
                if 'yuque.com/api' in request.url and request.headers:
                    yuque_requests.append(request)

            if not yuque_requests:
                logger.warning("未找到语雀API请求，尝试触发API请求...")
                # 尝试刷新页面或访问API以触发请求
                current_url = self.driver.current_url
                self.driver.refresh()
                time.sleep(5)
                
                # 再次检查
                for request in self.driver.requests:
                    if 'yuque.com/api' in request.url and request.headers:
                        yuque_requests.append(request)
                
                if not yuque_requests:
                    logger.warning("仍未找到语雀API请求，使用默认请求头")
                    return self._get_default_headers()

            # 获取最新的API请求头
            latest_request = yuque_requests[-1]
            headers = {}

            # 提取关键请求头
            important_headers = [
                'Authorization', 'Cookie', 'X-Requested-With', 'Referer',
                'User-Agent', 'Accept', 'Accept-Language', 'Accept-Encoding',
                'Connection', 'Sec-Fetch-Dest', 'Sec-Fetch-Mode', 'Sec-Fetch-Site',
                'X-CSRF-Token', 'X-Auth-Token', 'Origin'
            ]

            for header_name in important_headers:
                header_value = latest_request.headers.get(header_name)
                if header_value:
                    headers[header_name] = header_value

            # 从当前页面获取CSRF token（更准确）
            csrf_token = self._extract_csrf_token_from_page()
            if csrf_token:
                headers['X-CSRF-Token'] = csrf_token
                logger.info(f"从页面获取到CSRF token: {csrf_token[:10]}...")

            # 确保有基本的请求头
            current_url = self.driver.current_url
            if 'fjzx.yuque.com' in current_url:
                base_url = 'https://fjzx.yuque.com'
            else:
                base_url = 'https://www.yuque.com'

            default_headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Referer': base_url + '/',
                'Origin': base_url,
                'X-Requested-With': 'XMLHttpRequest',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
            }

            # 用默认值填充缺失的头部
            for key, value in default_headers.items():
                if key not in headers:
                    headers[key] = value

            self.captured_headers = headers
            self.save_headers()

            logger.info(f"成功捕获 {len(headers)} 个请求头")
            print(f"   ✅ 捕获到 {len(headers)} 个请求头")
            return headers

        except Exception as e:
            logger.error(f"捕获请求头失败: {e}")
            return self._get_default_headers()

    def _extract_csrf_token_from_page(self) -> Optional[str]:
        """从当前页面提取CSRF token"""
        try:
            # 尝试从meta标签获取CSRF token
            csrf_token = self.driver.execute_script("""
                // 尝试从meta标签获取
                var token = document.querySelector('meta[name="csrf-token"]');
                if (token) return token.getAttribute('content');
                
                var csrfMeta = document.querySelector('meta[name="_token"]');
                if (csrfMeta) return csrfMeta.getAttribute('content');
                
                // 尝试从页面脚本中提取
                var scripts = document.querySelectorAll('script');
                for (var i = 0; i < scripts.length; i++) {
                    var script = scripts[i];
                    var text = script.innerHTML;
                    
                    // 查找CSRF token模式
                    var patterns = [
                        /csrf[_-]?token['"]?\s*[:=]\s*['"]([a-zA-Z0-9\-_]{20,})['"]?/i,
                        /_token['"]?\s*[:=]\s*['"]([a-zA-Z0-9\-_]{20,})['"]?/i,
                        /window\.csrf\s*=\s*['"]([a-zA-Z0-9\-_]{20,})['"]?/i
                    ];
                    
                    for (var j = 0; j < patterns.length; j++) {
                        var match = text.match(patterns[j]);
                        if (match && match[1]) return match[1];
                    }
                }
                
                // 尝试从window对象获取
                if (typeof window.csrf_token !== 'undefined') return window.csrf_token;
                if (typeof window._token !== 'undefined') return window._token;
                if (window.yuque && typeof window.yuque.csrf_token !== 'undefined') return window.yuque.csrf_token;
                
                return null;
            """)
            
            return csrf_token if csrf_token else None
            
        except Exception as e:
            logger.warning(f"从页面提取CSRF token失败: {e}")
            return None

    def _get_default_headers(self) -> Dict[str, str]:
        """获取默认请求头"""
        return {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
        }

    def save_headers(self) -> bool:
        """保存捕获的请求头"""
        try:
            with open(self.headers_file, 'w', encoding='utf-8') as f:
                json.dump(self.captured_headers, f, ensure_ascii=False, indent=2)

            logger.info("请求头保存成功")
            return True

        except Exception as e:
            logger.error(f"保存请求头失败: {e}")
            return False

    def load_headers(self) -> Dict[str, str]:
        """加载保存的请求头"""
        if not os.path.exists(self.headers_file):
            return self._get_default_headers()

        try:
            with open(self.headers_file, 'r', encoding='utf-8') as f:
                headers = json.load(f)

            self.captured_headers = headers
            logger.info("请求头加载成功")
            return headers

        except Exception as e:
            logger.error(f"加载请求头失败: {e}")
            return self._get_default_headers()

    def get_headers(self) -> Dict[str, str]:
        """获取当前的请求头"""
        if not self.captured_headers:
            return self.load_headers()
        return self.captured_headers
    
    def close(self):
        """关闭WebDriver"""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("WebDriver已关闭")
            except Exception as e:
                logger.error(f"关闭WebDriver失败: {e}")
            finally:
                self.driver = None
                self.wait = None
