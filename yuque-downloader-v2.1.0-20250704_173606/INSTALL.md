# 📦 语雀下载工具 - 安装指南

## 🎯 快速安装（推荐）

### 步骤1: 检查Python环境
```bash
# 检查Python版本（需要3.8+）
python --version
# 或
python3 --version

# 如果没有Python，请先安装：
# Windows: 从 https://python.org 下载安装
# macOS: brew install python
# Ubuntu: sudo apt update && sudo apt install python3 python3-pip
```

### 步骤2: 下载程序
```bash
# 方式A: 直接下载ZIP包（推荐）
# 下载: https://github.com/yuque-downloader/yuque-downloader/archive/main.zip
# 解压后进入目录

# 方式B: 使用Git克隆
git clone https://github.com/yuque-downloader/yuque-downloader.git
cd yuque-downloader
```

### 步骤3: 安装依赖
```bash
# 安装所需依赖包
pip install -r requirements.txt

# 如果提示权限错误，使用：
pip install -r requirements.txt --user

# 如果使用conda环境：
conda install --file requirements.txt -c conda-forge
```

### 步骤4: 运行程序
```bash
# 启动程序
python main.py

# 首次运行时，程序会：
# 1. 自动下载ChromeDriver
# 2. 启动浏览器
# 3. 等待您登录语雀账号
```

## 🔧 环境要求详解

### Python环境
- **版本**: Python 3.8 或更高版本
- **包管理**: pip 21.0+ 或 conda 4.10+
- **虚拟环境**: 推荐使用venv或conda环境

### 操作系统支持
| 系统 | 版本要求 | 说明 |
|------|----------|------|
| Windows | 10+ | 推荐使用PowerShell或CMD |
| macOS | 10.14+ | 支持Intel和Apple Silicon |
| Linux | Ubuntu 18.04+, CentOS 7+ | 需要安装Chrome浏览器 |

### 浏览器要求
- **Chrome浏览器**: 88+版本
- **ChromeDriver**: 程序自动下载管理
- **网络连接**: 需要能够访问yuque.com

## ⚙️ 详细安装步骤

### Windows用户

1. **安装Python**
```cmd
# 下载并安装Python（勾选"Add to PATH"）
# https://www.python.org/downloads/windows/

# 验证安装
python --version
pip --version
```

2. **下载程序**
```cmd
# 下载ZIP文件并解压，或使用Git
git clone https://github.com/yuque-downloader/yuque-downloader.git
cd yuque-downloader
```

3. **安装依赖**
```cmd
# 使用pip安装
pip install -r requirements.txt

# 如果出现SSL错误，使用：
pip install -r requirements.txt --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org
```

4. **运行程序**
```cmd
python main.py
```

### macOS用户

1. **安装Python**
```bash
# 使用Homebrew（推荐）
brew install python

# 或下载官方安装包
# https://www.python.org/downloads/macos/
```

2. **下载程序**
```bash
git clone https://github.com/yuque-downloader/yuque-downloader.git
cd yuque-downloader
```

3. **安装依赖**
```bash
# 创建虚拟环境（推荐）
python3 -m venv yuque_env
source yuque_env/bin/activate

# 安装依赖
pip install -r requirements.txt
```

4. **运行程序**
```bash
python main.py
```

### Linux用户

1. **安装Python和Chrome**
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3 python3-pip python3-venv
sudo apt install google-chrome-stable

# CentOS/RHEL
sudo yum install python3 python3-pip
# Chrome需要手动下载安装包
```

2. **下载程序**
```bash
git clone https://github.com/yuque-downloader/yuque-downloader.git
cd yuque-downloader
```

3. **安装依赖**
```bash
# 创建虚拟环境
python3 -m venv yuque_env
source yuque_env/bin/activate

# 安装依赖
pip install -r requirements.txt
```

4. **运行程序**
```bash
python main.py
```

## 🐛 常见安装问题

### 问题1: Python版本过低
```bash
# 错误信息: "Python 3.8+ required"
# 解决方案: 升级Python版本

# 查看当前版本
python --version

# 安装最新版本
# Windows: 从官网下载安装
# macOS: brew upgrade python  
# Linux: sudo apt install python3.11
```

### 问题2: pip安装失败
```bash
# 错误信息: "Permission denied" 或 "Access denied"
# 解决方案: 使用用户模式安装

pip install -r requirements.txt --user

# 或使用虚拟环境
python -m venv myenv
# Windows: myenv\Scripts\activate
# macOS/Linux: source myenv/bin/activate
pip install -r requirements.txt
```

### 问题3: ChromeDriver下载失败
```bash
# 错误信息: "ChromeDriver download failed"
# 解决方案: 手动下载ChromeDriver

# 1. 检查Chrome版本: chrome://version/
# 2. 下载对应版本: https://chromedriver.chromium.org/
# 3. 放置在程序目录的drivers文件夹中
```

### 问题4: selenium-wire安装问题
```bash
# 错误信息: "Microsoft Visual C++ 14.0 is required"
# 解决方案（Windows）:
# 1. 安装 Visual Studio Build Tools
# 2. 或使用预编译包: pip install --only-binary=all selenium-wire
```

### 问题5: 网络连接问题
```bash
# 如果pip下载慢，使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 或配置pip镜像
# Windows: %APPDATA%\pip\pip.ini
# macOS/Linux: ~/.pip/pip.conf
# 内容:
[global]
index-url = https://pypi.tuna.tsinghua.edu.cn/simple/
trusted-host = pypi.tuna.tsinghua.edu.cn
```

## ✅ 验证安装

安装完成后，运行以下命令验证：

```bash
# 验证Python模块
python -c "import selenium, seleniumwire, requests, tqdm; print('所有依赖安装成功！')"

# 启动程序测试
python main.py

# 预期输出:
# 🚀 语雀知识库批量下载工具启动
# 🔧 正在初始化拦截器...
# 🌐 正在启动浏览器...
```

## 🚀 首次运行

1. **启动程序**: `python main.py`
2. **浏览器登录**: 程序会打开Chrome浏览器，请登录您的语雀账号
3. **等待拦截**: 浏览器会自动访问语雀并拦截API
4. **选择下载**: 根据提示选择知识库和下载模式
5. **开始下载**: 确认后程序会自动下载文档

## 📞 获取帮助

如果遇到安装问题：

1. **查看日志**: 检查 `yuque_downloader.log` 文件
2. **GitHub Issues**: https://github.com/yuque-downloader/yuque-downloader/issues
3. **讨论区**: https://github.com/yuque-downloader/yuque-downloader/discussions

---
💡 **提示**: 建议在虚拟环境中安装，避免与其他Python项目冲突！ 