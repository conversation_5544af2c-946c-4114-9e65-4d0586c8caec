#!/bin/bash
echo "🚀 语雀知识库批量下载工具"
echo "==============================="
echo

echo "🔧 检查Python环境..."
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo "❌ 错误: 未找到Python，请先安装Python 3.8+"
    echo "📥 安装指令:"
    echo "   macOS: brew install python"
    echo "   Ubuntu: sudo apt install python3 python3-pip"
    exit 1
fi

# 尝试使用python3，如果不存在则使用python
if command -v python3 &> /dev/null; then
    PYTHON_CMD=python3
    PIP_CMD=pip3
else
    PYTHON_CMD=python
    PIP_CMD=pip
fi

echo "✅ Python环境正常"
echo

echo "🔧 检查依赖包..."
$PYTHON_CMD -c "import selenium, seleniumwire, requests, tqdm" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "📦 正在安装依赖包..."
    $PIP_CMD install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败，请手动运行: $PIP_CMD install -r requirements.txt"
        exit 1
    fi
fi

echo "✅ 依赖检查完成"
echo

echo "🎯 启动程序..."
echo "==============================="
$PYTHON_CMD main.py
