# 🚀 语雀下载工具 v2.1.0 - 快速开始

## ✨ 新功能亮点 (v2.1.0)
- � **Excel文档自动下载**: 支持自动下载Excel表格文档(.xlsx)
- 🔧 **文件名修复**: Excel文档现在正确显示.xlsx扩展名
- 📊 **统一处理**: Excel文档与Word文档使用相同的下载流程

## �🎯 一键启动

### Windows用户
双击运行 `start.bat` 文件

### macOS/Linux用户
在终端中运行：
```bash
chmod +x start.sh
./start.sh
```

## 📋 使用流程

1. **首次启动**: 程序会自动检查和安装依赖
2. **浏览器登录**: 在打开的Chrome浏览器中登录语雀账号
3. **选择知识库**: 从拦截到的列表中选择要下载的知识库
4. **选择模式**:
   - 全部下载: 下载选中知识库的所有文档
   - 精选下载: 为每个知识库单独选择文档
5. **开始下载**: 确认后程序自动下载到 `downloads/` 目录

## 📊 支持的文档类型

- 📝 **Word文档** (.docx) - 普通文档和富文本内容
- 📊 **Excel表格** (.xlsx) - 数据表格和图表 ✨新增
- 📋 **其他格式** - 根据导出设置支持PDF、Markdown等

## 📁 文件说明

- `main.py` - 程序主入口
- `requirements.txt` - Python依赖列表
- `README.md` - 详细说明文档
- `INSTALL.md` - 安装指南
- `EXCEL_DOWNLOAD_SUMMARY.md` - Excel功能详细说明 ✨新增
- `FILENAME_EXTENSION_FIX.md` - 文件名修复说明 ✨新增
- `downloads/` - 下载文件存储目录
- `logs/` - 程序日志目录

## ❓ 常见问题

**Q: 浏览器无法启动？**
A: 确保已安装Chrome浏览器，程序会自动下载ChromeDriver

**Q: 权限错误？**  
A: Windows右键"以管理员身份运行"，macOS/Linux使用sudo

**Q: 网络错误？**
A: 检查网络连接，确保能访问yuque.com

## 📞 技术支持

- 查看详细文档: README.md
- 安装问题: INSTALL.md  
- 问题反馈: GitHub Issues

---
💡 提示: 首次使用建议阅读 README.md 获取完整功能说明
