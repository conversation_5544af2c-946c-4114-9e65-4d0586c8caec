#!/usr/bin/env python3
"""
语雀API客户端使用示例
展示如何使用改进后的分离式文档下载功能
"""

import logging
from api_client import YuqueAPIClient

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def example_usage():
    """使用示例"""
    
    # 假设已经有了webdriver_manager实例
    # webdriver_manager = WebDriverManager()
    
    # 创建API客户端
    # api_client = YuqueAPIClient(webdriver_manager)
    
    print("=== 语雀API客户端改进功能使用示例 ===\n")
    
    # 示例1：导出普通文档
    print("1. 导出普通文档（Word/Markdown）:")
    print("   result = api_client.export_doc(doc_id='12345', doc_type='doc')")
    print("   # 使用Word格式导出，适用于富文本、Markdown等文档\n")
    
    # 示例2：导出表格文档
    print("2. 导出表格文档（Excel）:")
    print("   result = api_client.export_doc(doc_id='12345', doc_type='sheet')")
    print("   # 使用Excel格式导出，专门处理电子表格文档\n")
    
    # 示例3：下载普通文档
    print("3. 下载普通文档:")
    print("   success = api_client.download_file(")
    print("       url='https://example.com/doc.docx',")
    print("       file_path='./downloads/document.docx',")
    print("       doc_type='doc'")
    print("   )")
    print("   # 使用普通文档下载策略\n")
    
    # 示例4：下载表格文档
    print("4. 下载表格文档:")
    print("   success = api_client.download_file(")
    print("       url='https://example.com/sheet.xlsx',")
    print("       file_path='./downloads/spreadsheet.xlsx',")
    print("       doc_type='sheet'")
    print("   )")
    print("   # 使用表格文档专用下载策略，处理OSS重定向\n")
    
    # 示例5：兼容性使用（不指定文档类型）
    print("5. 兼容性使用（向后兼容）:")
    print("   result = api_client.export_doc(doc_id='12345')")
    print("   success = api_client.download_file(url, file_path)")
    print("   # 不指定doc_type时，默认使用普通文档处理流程\n")

def demonstrate_differences():
    """展示不同文档类型的处理差异"""
    
    print("=== 不同文档类型的处理差异 ===\n")
    
    print("普通文档 (doc_type='doc' 或不指定):")
    print("  导出参数: {'type': 'word', 'force': 0}")
    print("  下载策略: 直接下载 → 语雀认证头 → 完整认证")
    print("  适用于: Markdown、富文本、普通文档\n")
    
    print("表格文档 (doc_type='sheet'):")
    print("  导出参数: {'type': 'excel', 'force': 0}")
    print("  下载策略: 表格专用下载 → 语雀认证头 → 完整认证 → 直接下载")
    print("  特殊处理: OSS重定向、Excel专用Accept头、URL构建")
    print("  适用于: 电子表格、Excel文档\n")

def show_error_handling():
    """展示错误处理改进"""
    
    print("=== 错误处理改进 ===\n")
    
    print("分类错误信息:")
    print("  - 普通文档导出失败: '普通文档导出请求失败: {doc_id}'")
    print("  - 表格文档导出失败: '表格文档导出请求失败: {doc_id}'")
    print("  - 普通文档下载失败: '所有普通文档下载策略都失败了'")
    print("  - 表格文档下载失败: '所有表格文档下载策略都失败了'\n")
    
    print("详细日志记录:")
    print("  - 记录使用的导出参数和文档类型")
    print("  - 记录尝试的下载策略和失败原因")
    print("  - 记录URL构建和重定向处理过程")
    print("  - 区分不同类型文档的处理状态\n")

def show_technical_improvements():
    """展示技术改进要点"""
    
    print("=== 技术改进要点 ===\n")
    
    print("1. 架构分离:")
    print("   - export_doc() 作为入口，根据doc_type分发到专门方法")
    print("   - _export_regular_doc() 处理普通文档")
    print("   - _export_sheet_doc() 处理表格文档")
    print("   - 各自使用不同的导出参数和轮询逻辑\n")
    
    print("2. 下载策略优化:")
    print("   - _download_regular_file() 使用标准下载流程")
    print("   - _download_sheet_file() 使用表格专用流程")
    print("   - _download_sheet_with_special_headers() 处理Excel特殊需求\n")
    
    print("3. URL处理增强:")
    print("   - _construct_excel_download_url() 构建完整Excel下载URL")
    print("   - 自动处理相对路径和绝对路径")
    print("   - 支持企业版和公版语雀环境\n")
    
    print("4. 重定向处理:")
    print("   - 自动跟踪302/301重定向")
    print("   - 智能替换/attachments为OSS地址")
    print("   - 处理多级重定向场景\n")

if __name__ == "__main__":
    example_usage()
    print("\n" + "="*50 + "\n")
    demonstrate_differences()
    print("\n" + "="*50 + "\n")
    show_error_handling()
    print("\n" + "="*50 + "\n")
    show_technical_improvements()
