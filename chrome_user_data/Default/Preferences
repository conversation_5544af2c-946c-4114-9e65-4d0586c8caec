{"NewTabPage": {"PrevNavigationTime": "*****************"}, "accessibility": {"captions": {"headless_caption_enabled": false, "live_caption_language": "cmn-Hans-CN"}}, "account_tracker_service_last_update": "*****************", "ack_existing_ntp_extensions": true, "alternate_error_pages": {"backup": false}, "autocomplete": {"retention_policy_last_version": 137}, "autofill": {"last_version_deduped": 137}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"check_default_browser": false, "window_placement": {"bottom": 1120, "left": 1920, "maximized": false, "right": 3432, "top": 176, "work_area_bottom": 1120, "work_area_left": 1920, "work_area_right": 3432, "work_area_top": 176}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 17230, "default_search_provider": {"guid": ""}, "devtools": {"last_open_timestamp": "**************", "preferences": {"closeable-tabs": "{\"security\":true,\"freestyler\":true,\"chrome-recorder\":true}", "currentDockState": "\"right\"", "elements.styles.sidebar.width": "{\"vertical\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "inspector-view.split-view-state": "{\"vertical\":{\"size\":1057.6328125}}", "inspector.drawer-split-view-state": "{\"horizontal\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "inspectorVersion": "38", "network-panel-sidebar-state": "{\"vertical\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "network-panel-split-view-state": "{\"vertical\":{\"size\":0}}", "network-panel-split-view-waterfall": "{\"vertical\":{\"size\":0}}", "network-text-filter": "\"user_books\"", "panel-selected-tab": "\"network\"", "releaseNoteVersionSeen": "78", "request-info-form-data-category-expanded": "true", "request-info-general-category-expanded": "true", "request-info-query-string-category-expanded": "true", "request-info-request-headers-category-expanded": "true", "request-info-request-payload-category-expanded": "true", "request-info-response-headers-category-expanded": "true", "resource-view-tab": "\"payload\"", "selected-profile-type": "\"HEAP\"", "sources-panel-navigator-split-view-state": "{\"vertical\":{\"size\":0,\"showMode\":\"Both\"}}", "sources-panel-split-view-state": "{\"vertical\":{\"size\":0,\"showMode\":\"Both\"}}", "styles-pane-sidebar-tab-order": "{\"styles\":10,\"computed\":20}"}, "synced_preferences_sync_disabled": {"adorner-settings": "[{\"adorner\":\"grid\",\"isEnabled\":true},{\"adorner\":\"subgrid\",\"isEnabled\":true},{\"adorner\":\"flex\",\"isEnabled\":true},{\"adorner\":\"ad\",\"isEnabled\":true},{\"adorner\":\"scroll-snap\",\"isEnabled\":true},{\"adorner\":\"container\",\"isEnabled\":true},{\"adorner\":\"slot\",\"isEnabled\":true},{\"adorner\":\"top-layer\",\"isEnabled\":true},{\"adorner\":\"reveal\",\"isEnabled\":true},{\"adorner\":\"media\",\"isEnabled\":false},{\"adorner\":\"scroll\",\"isEnabled\":true}]", "syncedInspectorVersion": "38"}}, "distribution": {"import_bookmarks": false, "import_history": false, "import_search_engine": false, "make_chrome_default_for_user": false, "skip_first_run_ui": true}, "dns_prefetching": {"enabled": false}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "bcd602e1-6ec8-4a0d-9d8a-4ad426ce1d62", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}}, "gaia_cookie": {"changed_time": **********.265977, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "com.chrome.macosx"}, "google": {"services": {"signin_scoped_device_id": "d5e7d58e-3668-406c-bb27-935ef1e780ad"}}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "*****************", "recent_session_start_times": ["*****************", "*****************", "*****************"], "session_last_active_time": "*****************", "session_start_time": "*****************"}, "intl": {"selected_languages": "zh-CN,zh"}, "invalidation": {"per_sender_topics_to_handler": {"*************": {}}}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "foxjWuSRz261Ks7ltz1qVKl0evcDAW5u48FH7q2p/Qm7i77XDkeNrQVTKU7bbL05b5QRCBgfuN5b7T3Xo+vFTg=="}, "ntp": {"num_personal_suggestions": 4}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "predictionmodelfetcher": {"last_fetch_attempt": "*****************", "last_fetch_success": "*****************"}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PRICE_TRACKING": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true, "relaunch_chrome_bubble_dismissed_counter": 0}, "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 8, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"https://[*.]yuque.com,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"https://fjzx.yuque.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 32}}, "https://www.yuque.com:443,*": {"expiration": "13403870394289895", "last_modified": "13396094394289898", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 5}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"chrome://newtab/,*": {"last_modified": "13396094311124862", "setting": {"lastEngagementTime": 1.3396094311124848e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 6.0, "rawScore": 21.785759986834275}}, "https://fjzx.yuque.com:443,*": {"last_modified": "13396094327537252", "setting": {"lastEngagementTime": 1.3396094327537242e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 9.0, "rawScore": 26.192039509138276}}, "https://www.yuque.com:443,*": {"last_modified": "13396094344150037", "setting": {"lastEngagementTime": 1.3396094344150008e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 2.1, "rawScore": 5.617186302424548}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pattern_pairs": {"https://*,*": {"media-stream": {"audio": "<PERSON><PERSON><PERSON>", "video": "<PERSON><PERSON><PERSON>"}}}, "pref_version": 1}, "creation_time": "13395296570500929", "default_content_setting_values": {"geolocation": 1, "notifications": 2}, "default_content_settings": {"geolocation": 1, "mouselock": 1, "notifications": 1, "popups": 1, "ppapi-broker": 1}, "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "exit_type": "Normal", "family_member_role": "not_in_family", "last_engagement_time": "13396094344150007", "last_time_obsolete_http_credentials_removed": 1750823031.129027, "last_time_password_store_metrics_reported": 1751618731.990668, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_default_content_settings": {"images": 1}, "managed_user_id": "", "name": "您的 Chrome", "password_hash_data_list": [], "password_manager_enabled": false, "were_old_google_logins_removed": true}, "safebrowsing": {"enabled": false, "event_timestamps": {}, "metrics_last_log_time": "13396092301", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "search": {"suggest_enabled": false}, "segmentation_platform": {"client_result_prefs": "ClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQz/L52sz15RcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADEMbz+drM9eUXCnMKFXBhc3N3b3JkX21hbmFnZXJfdXNlchJaCk8NAAAAABCJ8/nazPXlFxo9CjUKMw0AAAA/EhNQYXNzd29yZE1hbmFnZXJVc2VyGhdOb3RfUGFzc3dvcmRNYW5hZ2VyVXNlchIEEAcYBCABEOTz+drM9eUXCmQKC3NlYXJjaF91c2VyElUKSg0AAAAAEPrz+drM9eUXGjgKMBouCgoNAACAPxIDTG93Cg0NAACgQBIGTWVkaXVtCgsNAACwQRIESGlnaBIETm9uZRIEEAcYBCACEIz0+drM9eUXCuUCChFjcm9zc19kZXZpY2VfdXNlchLPAgrDAg0AAIA/EJjy+drM9eUXGrACCqcCGqQCChkNAACAPxISTm9Dcm9zc0RldmljZVVzYWdlChgNAAAAQBIRQ3Jvc3NEZXZpY2VNb2JpbGUKGQ0AAEBAEhJDcm9zc0RldmljZURlc2t0b3AKGA0AAIBAEhFDcm9zc0RldmljZVRhYmxldAoiDQAAoEASG0Nyb3NzRGV2aWNlTW9iaWxlQW5kRGVza3RvcAohDQAAwEASGkNyb3NzRGV2aWNlTW9iaWxlQW5kVGFibGV0CiINAADgQBIbQ3Jvc3NEZXZpY2VEZXNrdG9wQW5kVGFibGV0CiANAAAAQRIZQ3Jvc3NEZXZpY2VBbGxEZXZpY2VUeXBlcwoXDQAAEEESEENyb3NzRGV2aWNlT3RoZXISEk5vQ3Jvc3NEZXZpY2VVc2FnZRIEEAcYBCACEOXy+drM9eUXCmAKEXJlc3VtZV9oZWF2eV91c2VyEksKQA0AAAAAEL7y+drM9eUXGi4KJgokDQAAAD8SFlJlc3VtZUhlYXZ5VXNlclNlZ21lbnQaBU90aGVyEgQQDhgEIAIQ/PL52sz15Rc=", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13395974399000000", "uma_in_sql_start_time": "13395296571019151"}, "sessions": {"event_log": [{"crashed": false, "time": "13395318935824655", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13395318944179614", "type": 2, "window_count": 1}, {"crashed": false, "time": "13395319136289983", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13395319144152973", "type": 2, "window_count": 1}, {"crashed": false, "time": "13395319187480896", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13395319198717376", "type": 2, "window_count": 1}, {"crashed": false, "time": "13395319501574140", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13395319510123266", "type": 2, "window_count": 1}, {"crashed": false, "time": "13395320207102923", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13395320215118806", "type": 2, "window_count": 1}, {"crashed": false, "time": "13395320500678681", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13395320508883412", "type": 2, "window_count": 1}, {"crashed": false, "time": "13395409459964254", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13395409488335140", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396092301853785", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "*****************", "type": 2, "window_count": 1}, {"crashed": false, "time": "*****************", "type": 0}, {"crashed": true, "time": "*****************", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "*****************", "type": 2, "window_count": 1}], "session_data_status": 5}, "settings": {"force_google_safesearch": false}, "signin": {"allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "sync": {"passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate": {"enabled": false}, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "137"}}