#!/usr/bin/env python3
"""
测试下载策略的脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_download_strategies():
    """测试下载策略"""
    try:
        # 创建一个最小的测试环境
        class MockWebDriverManager:
            def __init__(self):
                self.driver = None
            def get_headers(self):
                return {}
            def close(self):
                pass
        
        from api_client import YuqueAPIClient
        
        mock_wm = MockWebDriverManager()
        client = YuqueAPIClient(mock_wm)
        
        test_url = "https://lark-temp.oss-cn-hangzhou.aliyuncs.com/__temp/0/docx/2a896a05-7f0a-43cc-8ec3-abaa9df5c13a.docx?OSSAccessKeyId=LTAI4GKnqTWmz2X8mzA1Sjbv&Expires=1751626205&Signature=XDC%2FHl8g1JR0nga05%2F%2Bgi307gbU%3D"
        
        # 检查URL类型
        url_type = client._analyze_url_type(test_url)
        print(f"URL类型: {url_type}")
        
        # 检查是否有新的下载方法
        has_oss_signed_url = hasattr(client, '_download_oss_signed_url')
        has_oss_minimal_headers = hasattr(client, '_download_oss_minimal_headers')
        
        print(f"是否有 _download_oss_signed_url 方法: {has_oss_signed_url}")
        print(f"是否有 _download_oss_minimal_headers 方法: {has_oss_minimal_headers}")
        
        # 测试下载文件方法的策略选择逻辑
        test_dir = Path("./test_downloads")
        test_dir.mkdir(exist_ok=True)
        test_file = test_dir / "test_document.docx"
        
        print(f"\n开始测试下载...")
        print(f"测试文件: {test_file}")
        
        # 模拟下载过程，但不实际下载
        if url_type == 'oss_standard':
            print("✅ 检测到OSS标准URL，应该使用OSS专用策略")
            if has_oss_signed_url and has_oss_minimal_headers:
                print("✅ OSS专用下载方法存在")
                
                # 测试OSS签名URL下载方法
                try:
                    result = client._download_oss_signed_url(test_url, str(test_file))
                    print(f"OSS签名URL下载测试结果: {result}")
                except Exception as e:
                    print(f"OSS签名URL下载测试失败: {e}")
                
            else:
                print("❌ OSS专用下载方法不存在")
        else:
            print("❌ URL类型不是oss_standard")
        
        # 清理测试文件
        if test_file.exists():
            test_file.unlink()
        
        client.close()
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_download_file_method():
    """测试download_file方法的完整逻辑"""
    try:
        class MockWebDriverManager:
            def __init__(self):
                self.driver = None
            def get_headers(self):
                return {}
            def close(self):
                pass
        
        from api_client import YuqueAPIClient
        
        mock_wm = MockWebDriverManager()
        client = YuqueAPIClient(mock_wm)
        
        test_url = "https://lark-temp.oss-cn-hangzhou.aliyuncs.com/__temp/0/docx/2a896a05-7f0a-43cc-8ec3-abaa9df5c13a.docx?OSSAccessKeyId=LTAI4GKnqTWmz2X8mzA1Sjbv&Expires=1751626205&Signature=XDC%2FHl8g1JR0nga05%2F%2Bgi307gbU%3D"
        
        test_dir = Path("./test_downloads")
        test_dir.mkdir(exist_ok=True)
        test_file = test_dir / "test_document.docx"
        
        print(f"\n测试完整的download_file方法...")
        
        # 检查download_file方法的源码
        import inspect
        source = inspect.getsource(client.download_file)
        print("download_file方法源码片段:")
        lines = source.split('\n')
        for i, line in enumerate(lines[:20]):  # 只显示前20行
            print(f"{i+1:2d}: {line}")
        
        if "oss_standard" in source:
            print("✅ download_file方法包含OSS处理逻辑")
        else:
            print("❌ download_file方法不包含OSS处理逻辑")
        
        client.close()
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🔧 下载策略测试工具")
    print("=" * 50)
    
    # 测试1: 下载策略
    print("\n1. 测试下载策略:")
    test_download_strategies()
    
    # 测试2: download_file方法
    print("\n2. 测试download_file方法:")
    test_download_file_method()

if __name__ == "__main__":
    main()
